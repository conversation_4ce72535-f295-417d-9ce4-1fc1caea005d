#!/usr/bin/env tsx

/**
 * Core Revenue-Generating Workflow Validation
 * Validates that the clinical workflow → billing pipeline works correctly
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function validateCoreWorkflow() {
  console.log('🏥 Testing Core Revenue-Generating Workflow');
  console.log('===========================================\n');

  try {
    // Step 1: Create tenant and admin user
    console.log('1️⃣ Creating tenant and admin user...');
    const tenant = await prisma.tenant.create({
      data: {
        id: `workflow-test-${Date.now()}`,
        name: 'Workflow Test Clinic'
      }
    });

    const adminUser = await prisma.user.create({
      data: {
        tenantId: tenant.id,
        firstName: 'Dr.',
        lastName: 'Smith',
        phoneNumber: '+**********',
        email: '<EMAIL>',
        username: 'drsmith',
        password: 'hashedpassword',
        userType: 'DENTIST'
      }
    });
    console.log('✅ Tenant and admin user created');

    // Step 2: Register patient
    console.log('\n2️⃣ Registering patient...');
    const patientUser = await prisma.user.create({
      data: {
        tenantId: tenant.id,
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '+**********',
        email: '<EMAIL>',
        username: 'johndoe',
        password: 'hashedpassword',
        userType: 'PATIENT'
      }
    });

    const patient = await prisma.patient.create({
      data: {
        tenantId: tenant.id,
        userId: patientUser.id,
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: new Date('1985-06-15'),
        phoneNumber: '+**********',
        email: '<EMAIL>',
        address: '123 Main St, City, State'
      }
    });
    console.log('✅ Patient registered');

    // Step 3: Create case sheet
    console.log('\n3️⃣ Creating case sheet...');
    const caseSheet = await prisma.caseSheet.create({
      data: {
        tenantId: tenant.id,
        patientId: patient.id,
        clinicalNotes: 'Initial consultation notes'
      }
    });
    console.log('✅ Case sheet created');

    // Step 4: Create teeth records
    console.log('\n4️⃣ Creating teeth records...');
    const teeth = [];
    for (let i = 11; i <= 18; i++) { // Upper right quadrant
      const tooth = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: i,
          quadrant: Math.floor(i / 10),
          positionInQuadrant: i % 10,
          toothName: `Tooth ${i}`,
          status: 'PRESENT'
        }
      });
      teeth.push(tooth);
    }
    console.log(`✅ ${teeth.length} teeth records created`);

    // Step 5: Schedule appointment
    console.log('\n5️⃣ Scheduling appointment...');
    const appointment = await prisma.appointment.create({
      data: {
        tenantId: tenant.id,
        patientId: patient.id,
        primaryProviderId: adminUser.id,
        appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        durationMinutes: 60,
        appointmentType: 'CONSULTATION',
        status: 'SCHEDULED',
        notes: 'Initial consultation'
      }
    });
    console.log('✅ Appointment scheduled');

    // Step 6: Document findings
    console.log('\n6️⃣ Documenting clinical findings...');
    const findings = [];
    
    // Finding 1: Cavity on tooth 16
    const finding1 = await prisma.finding.create({
      data: {
        tenantId: tenant.id,
        toothId: teeth[5].id, // Tooth 16
        description: 'Occlusal cavity on tooth 16, moderate depth',
        recordedById: adminUser.id,
        recordedDate: new Date()
      }
    });
    findings.push(finding1);

    // Finding 2: Plaque buildup on tooth 17
    const finding2 = await prisma.finding.create({
      data: {
        tenantId: tenant.id,
        toothId: teeth[6].id, // Tooth 17
        description: 'Heavy plaque buildup on tooth 17, requires cleaning',
        recordedById: adminUser.id,
        recordedDate: new Date()
      }
    });
    findings.push(finding2);
    
    console.log(`✅ ${findings.length} clinical findings documented`);

    // Step 7: Create treatment plans (direct finding-to-treatment workflow)
    console.log('\n7️⃣ Creating treatment plans...');
    const treatments = [];

    // Treatment 1: Filling for cavity
    const treatment1 = await prisma.treatment.create({
      data: {
        tenantId: tenant.id,
        findingId: finding1.id,
        procedureName: 'Composite Filling',
        cost: 150.00,
        status: 'PENDING'
      }
    });
    treatments.push(treatment1);

    // Treatment 2: Cleaning for plaque
    const treatment2 = await prisma.treatment.create({
      data: {
        tenantId: tenant.id,
        findingId: finding2.id,
        procedureName: 'Professional Cleaning',
        cost: 100.00,
        status: 'PENDING'
      }
    });
    treatments.push(treatment2);

    console.log(`✅ ${treatments.length} treatment plans created`);

    // Step 8: Complete treatments
    console.log('\n8️⃣ Completing treatments...');
    await prisma.treatment.update({
      where: { id: treatment1.id },
      data: {
        status: 'COMPLETED',
        completedDate: new Date(),
        completedById: adminUser.id
      }
    });

    await prisma.treatment.update({
      where: { id: treatment2.id },
      data: {
        status: 'COMPLETED',
        completedDate: new Date(),
        completedById: adminUser.id
      }
    });
    console.log('✅ Treatments completed');

    // Step 9: Generate invoice
    console.log('\n9️⃣ Generating invoice...');
    const totalAmount = treatments.reduce((sum, t) => sum + Number(t.cost), 0);
    
    const invoice = await prisma.invoice.create({
      data: {
        tenantId: tenant.id,
        patientId: patient.id,
        invoiceNumber: `INV-${Date.now()}`,
        invoiceDate: new Date(),
        totalAmount: totalAmount,
        amountPaid: 0.00,
        balanceDue: totalAmount,
        status: 'SENT'
      }
    });
    console.log(`✅ Invoice generated: ${invoice.invoiceNumber} for $${totalAmount}`);

    // Step 10: Process payments
    console.log('\n🔟 Processing payments...');
    
    // Partial payment
    const payment1 = await prisma.payment.create({
      data: {
        tenantId: tenant.id,
        patientId: patient.id,
        invoiceId: invoice.id,
        amount: 150.00,
        paymentDate: new Date(),
        paymentMethod: 'CARD',
        status: 'COMPLETED',
        notes: 'Partial payment for filling'
      }
    });

    // Update invoice
    await prisma.invoice.update({
      where: { id: invoice.id },
      data: {
        amountPaid: 150.00,
        balanceDue: totalAmount - 150.00,
        status: 'PAID'
      }
    });

    // Final payment
    const payment2 = await prisma.payment.create({
      data: {
        tenantId: tenant.id,
        patientId: patient.id,
        invoiceId: invoice.id,
        amount: 100.00,
        paymentDate: new Date(),
        paymentMethod: 'CASH',
        status: 'COMPLETED',
        notes: 'Final payment for cleaning'
      }
    });

    // Update invoice to fully paid
    await prisma.invoice.update({
      where: { id: invoice.id },
      data: {
        amountPaid: totalAmount,
        balanceDue: 0.00,
        status: 'PAID'
      }
    });

    console.log(`✅ Payments processed: $${payment1.amount} + $${payment2.amount} = $${totalAmount}`);

    // Step 11: Verify complete workflow
    console.log('\n✅ Verifying complete workflow...');
    
    const workflowVerification = await prisma.patient.findUnique({
      where: { id: patient.id },
      include: {
        caseSheet: {
          include: {
            teeth: {
              include: {
                findings: {
                  include: {
                    treatments: true
                  }
                }
              }
            }
          }
        },
        appointments: {
          include: {
            primaryProvider: true
          }
        },
        invoices: {
          include: {
            payments: true
          }
        }
      }
    });

    console.log('\n📊 WORKFLOW VERIFICATION RESULTS');
    console.log('================================');
    console.log(`Patient: ${workflowVerification?.firstName} ${workflowVerification?.lastName}`);
    console.log(`Case Sheet: ${workflowVerification?.caseSheet ? 'Created' : 'Missing'}`);
    console.log(`Teeth Records: ${workflowVerification?.caseSheet?.teeth.length || 0}`);
    console.log(`Appointments: ${workflowVerification?.appointments.length || 0}`);
    console.log(`Findings: ${workflowVerification?.caseSheet?.teeth.reduce((sum, tooth) => sum + tooth.findings.length, 0) || 0}`);
    console.log(`Treatments: ${workflowVerification?.caseSheet?.teeth.reduce((sum, tooth) => sum + tooth.findings.reduce((fSum, finding) => fSum + finding.treatments.length, 0), 0) || 0}`);
    console.log(`Invoices: ${workflowVerification?.invoices.length || 0}`);
    console.log(`Payments: ${workflowVerification?.invoices.reduce((sum, inv) => sum + inv.payments.length, 0) || 0}`);
    console.log(`Total Revenue: $${workflowVerification?.invoices.reduce((sum, inv) => sum + Number(inv.amountPaid), 0) || 0}`);

    // Clean up test data
    console.log('\n🧹 Cleaning up test data...');
    await prisma.tenant.delete({ where: { id: tenant.id } });
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 CORE WORKFLOW VALIDATION SUCCESSFUL!');
    console.log('All revenue-generating features working correctly:');
    console.log('  ✅ Patient registration');
    console.log('  ✅ Case sheet creation');
    console.log('  ✅ Appointment scheduling');
    console.log('  ✅ Clinical findings documentation');
    console.log('  ✅ Direct finding-to-treatment workflow');
    console.log('  ✅ Treatment completion tracking');
    console.log('  ✅ Invoice generation');
    console.log('  ✅ Payment processing');
    console.log('  ✅ Complete audit trail');

    return true;

  } catch (error) {
    console.error('❌ Core workflow validation failed:', error);
    return false;
  }
}

async function main() {
  try {
    const success = await validateCoreWorkflow();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ Validation script failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}