@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(0.98 0.01 15);
  --foreground: oklch(0.15 0.02 15);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 15);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 15);
  --primary: oklch(0.45 0.12 15);
  --primary-foreground: oklch(0.98 0.01 15);
  --secondary: oklch(0.95 0.01 10);
  --secondary-foreground: oklch(0.25 0.02 15);
  --muted: oklch(0.96 0.01 10);
  --muted-foreground: oklch(0.45 0.02 15);
  --accent: oklch(0.65 0.08 15);
  --accent-foreground: oklch(0.98 0.01 15);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.9 0.01 10);
  --input: oklch(0.98 0.01 15);
  --ring: oklch(0.45 0.12 15);
  --chart-1: oklch(0.45 0.12 15);
  --chart-2: oklch(0.55 0.10 20);
  --chart-3: oklch(0.35 0.08 10);
  --chart-4: oklch(0.65 0.08 15);
  --chart-5: oklch(0.75 0.06 12);
  --sidebar: oklch(0.97 0.01 12);
  --sidebar-foreground: oklch(0.2 0.02 15);
  --sidebar-primary: oklch(0.45 0.12 15);
  --sidebar-primary-foreground: oklch(0.98 0.01 15);
  --sidebar-accent: oklch(0.95 0.01 10);
  --sidebar-accent-foreground: oklch(0.25 0.02 15);
  --sidebar-border: oklch(0.9 0.01 10);
  --sidebar-ring: oklch(0.45 0.12 15);
}

.dark {
  --background: oklch(0.12 0.02 15);
  --foreground: oklch(0.95 0.01 15);
  --card: oklch(0.18 0.02 15);
  --card-foreground: oklch(0.95 0.01 15);
  --popover: oklch(0.18 0.02 15);
  --popover-foreground: oklch(0.95 0.01 15);
  --primary: oklch(0.65 0.15 15);
  --primary-foreground: oklch(0.12 0.02 15);
  --secondary: oklch(0.22 0.02 15);
  --secondary-foreground: oklch(0.95 0.01 15);
  --muted: oklch(0.22 0.02 15);
  --muted-foreground: oklch(0.65 0.03 15);
  --accent: oklch(0.75 0.10 15);
  --accent-foreground: oklch(0.12 0.02 15);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(0.25 0.02 15);
  --input: oklch(0.18 0.02 15);
  --ring: oklch(0.65 0.15 15);
  --chart-1: oklch(0.65 0.15 15);
  --chart-2: oklch(0.72 0.12 18);
  --chart-3: oklch(0.55 0.10 12);
  --chart-4: oklch(0.75 0.10 15);
  --chart-5: oklch(0.68 0.08 20);
  --sidebar: oklch(0.15 0.02 15);
  --sidebar-foreground: oklch(0.95 0.01 15);
  --sidebar-primary: oklch(0.65 0.15 15);
  --sidebar-primary-foreground: oklch(0.12 0.02 15);
  --sidebar-accent: oklch(0.22 0.02 15);
  --sidebar-accent-foreground: oklch(0.95 0.01 15);
  --sidebar-border: oklch(0.25 0.02 15);
  --sidebar-ring: oklch(0.65 0.15 15);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
