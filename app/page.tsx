// Modern minimalist landing page for dental clinic management system
// Based on Django-to-Prisma migration requirements for dental practice software

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold tracking-tight">
                Dental and Diagnosis
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                Login
              </Button>
              <Button size="sm">Register</Button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-24 sm:py-32">
        <div className="mx-auto max-w-4xl text-center">
          <Badge variant="secondary" className="mb-6">
            Professional dental practice management system
          </Badge>
          <h1 className="text-4xl font-bold tracking-tight text-foreground sm:text-6xl lg:text-7xl">
            Complete dental practice management with{" "}
            <span className="text-primary">modern</span> efficiency
          </h1>
          <p className="mt-8 text-lg leading-8 text-muted-foreground sm:text-xl max-w-2xl mx-auto">
            Streamline your dental practice operations. Dental and Diagnosis
            (DnD) combines patient management, appointment scheduling, treatment
            planning, and billing in one comprehensive platform.
          </p>
          <div className="mt-12 flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-base px-8 py-6">
              Get Started
            </Button>
            <Button variant="outline" size="lg" className="text-base px-8 py-6">
              Login
            </Button>
          </div>
          <p className="mt-6 text-sm text-muted-foreground">
            HIPAA compliant • Secure data management • Professional support
          </p>
        </div>
      </section>

      <Separator className="container mx-auto" />

      {/* Features Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div className="mx-auto max-w-2xl text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Everything your dental practice needs
          </h2>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            Comprehensive features designed to streamline dental practice
            management and improve patient care
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <div className="w-6 h-6 rounded bg-primary/20"></div>
              </div>
              <CardTitle className="text-xl">Patient Management</CardTitle>
              <CardDescription className="text-base">
                Comprehensive patient records, treatment history, and secure
                data management with HIPAA compliance
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <div className="w-6 h-6 rounded bg-primary/20"></div>
              </div>
              <CardTitle className="text-xl">Appointment Scheduling</CardTitle>
              <CardDescription className="text-base">
                Smart scheduling system with automated reminders, conflict
                resolution, and multi-provider support
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <div className="w-6 h-6 rounded bg-primary/20"></div>
              </div>
              <CardTitle className="text-xl">Treatment Planning</CardTitle>
              <CardDescription className="text-base">
                Digital treatment plans with cost estimates, progress tracking,
                and patient communication tools
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <div className="w-6 h-6 rounded bg-primary/20"></div>
              </div>
              <CardTitle className="text-xl">Billing & Insurance</CardTitle>
              <CardDescription className="text-base">
                Automated billing, insurance claim processing, and financial
                reporting with multi-tenant support
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <div className="w-6 h-6 rounded bg-primary/20"></div>
              </div>
              <CardTitle className="text-xl">
                Multi-Tenant Architecture
              </CardTitle>
              <CardDescription className="text-base">
                Perfect for dental groups with secure tenant isolation,
                role-based access, and centralized management
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="border-border/50 hover:border-border transition-colors">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
                <div className="w-6 h-6 rounded bg-primary/20"></div>
              </div>
              <CardTitle className="text-xl">Advanced Security</CardTitle>
              <CardDescription className="text-base">
                HIPAA-compliant security with encrypted data, audit trails, and
                role-based authentication systems
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </section>

      <Separator className="container mx-auto" />

      {/* Call to Action Section */}
      <section className="container mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <Card className="max-w-4xl mx-auto border-border/50">
          <CardContent className="p-12 text-center">
            <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl mb-6">
              Ready to modernize your dental practice?
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join hundreds of dental practices already using Dental and
              Diagnosis (DnD) to improve patient care and streamline operations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button size="lg" className="text-base px-8 py-6">
                Register Now
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="text-base px-8 py-6"
              >
                Login
              </Button>
            </div>
            <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
              <span>✓ HIPAA compliant</span>
              <span>✓ Secure & reliable</span>
              <span>✓ Professional support</span>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="border-t border-border/40 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <h3 className="text-lg font-semibold tracking-tight">
                Dental and Diagnosis
              </h3>
            </div>
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <a href="#" className="hover:text-foreground transition-colors">
                Privacy
              </a>
              <a href="#" className="hover:text-foreground transition-colors">
                Terms
              </a>
              <a href="#" className="hover:text-foreground transition-colors">
                Support
              </a>
              <a href="#" className="hover:text-foreground transition-colors">
                Contact
              </a>
            </div>
          </div>
          <Separator className="my-8" />
          <div className="text-center text-sm text-muted-foreground">
            © 2025 Dental and Diagnosis. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
