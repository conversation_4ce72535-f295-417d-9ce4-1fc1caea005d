# Common Fields Migration Guide

This guide explains the changes made to implement a consistent common fields pattern across all Prisma models.

## What Changed

### 1. Standardized Common Fields Pattern

All models now follow a consistent pattern with these fields:

```prisma
model ExampleModel {
  // Primary Key
  id        Int      @id @default(autoincrement())
  
  // Multi-tenancy
  tenantId  String
  tenant    Tenant   @relation(fields: [tenantId], references: [id])
  
  // Model-specific fields...
  
  // Audit Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Audit User Relations
  createdById Int?
  updatedById Int?
  createdBy   User? @relation("ModelCreatedBy", fields: [createdById], references: [id])
  updatedBy   User? @relation("ModelUpdatedBy", fields: [updatedById], references: [id])
  
  // Indexes
  @@index([tenantId, /* business fields */])
}
```

### 2. Models Updated

The following models were updated to include missing User relations:

#### Treatment Model
- ✅ Added `createdBy` and `updatedBy` User relations
- ✅ Added corresponding relations in User model

#### Invoice Model  
- ✅ Added `createdBy` and `updatedBy` User relations
- ✅ Added corresponding relations in User model

#### Payment Model
- ✅ Added `createdBy` and `updatedBy` User relations
- ✅ Added corresponding relations in User model

#### PaymentApplication Model
- ✅ Added `createdBy` and `updatedBy` User relations
- ✅ Added missing `tenant` relation
- ✅ Added corresponding relations in User and Tenant models

### 3. Enhanced Indexing

Added performance indexes to key models:
- `@@index([tenantId, createdAt])` for audit queries
- Improved existing tenant-based indexes

## New TypeScript Utilities

### Common Field Types (`/lib/types/common-fields.ts`)

```typescript
// Base interfaces
interface CommonFields {
  id: number
  tenantId: string
  createdAt: Date
  updatedAt: Date
  createdById: number | null
  updatedById: number | null
}

// Utility types
type CreateInput<T> = Omit<T, 'id' | 'createdAt' | 'updatedAt' | 'createdById' | 'updatedById'>
type UpdateInput<T> = Partial<Omit<T, 'id' | 'createdAt'>>
type WithCommonRelations<T> = T & { tenant: Tenant; createdBy: User | null; updatedBy: User | null }

// Helper functions
const createAuditData = (userId?: number) => ({ createdById: userId || null, updatedById: userId || null })
const createUpdateAuditData = (userId?: number) => ({ updatedById: userId || null })
```

### Prisma Extensions (`/lib/prisma-extensions/`)

```typescript
// Extensions provide enhanced functionality with built-in multi-tenancy
const patientExtension = Prisma.defineExtension({
  model: {
    patient: {
      // Enhanced methods with tenant isolation and audit tracking
      async createPatient(data: CreatePatientInput): Promise<Patient>
      async findByClinicId(clinicId: string, tenantId: string): Promise<Patient | null>
      async getActivePatients(tenantId: string): Promise<Patient[]>
      async searchPatients(criteria: SearchCriteria): Promise<Patient[]>
      // ... more methods
    }
  }
})
```

## Migration Steps for Developers

### 1. Update Your Queries

**Before:**
```typescript
const treatments = await prisma.treatment.findMany({
  where: { findingId: 123 }
})
```

**After:**
```typescript
const treatments = await prisma.treatment.findMany({
  where: { 
    findingId: 123,
    tenantId: currentTenantId // Always include tenantId
  },
  include: {
    createdBy: true, // Now available
    updatedBy: true, // Now available
  }
})
```

### 2. Update Create Operations

**Before:**
```typescript
const treatment = await prisma.treatment.create({
  data: {
    findingId: 123,
    procedureCode: "D1110",
    // ...
  }
})
```

**After:**
```typescript
const treatment = await prisma.treatment.create({
  data: {
    findingId: 123,
    procedureCode: "D1110",
    tenantId: currentTenantId,
    createdById: currentUserId,
    updatedById: currentUserId,
    // ...
  }
})

// Or use Prisma extensions with enhanced functionality:
const treatment = await prisma.treatment.createTreatment({
  ...treatmentData,
  tenantId: currentTenantId,
  createdById: currentUserId
})
```

### 3. Update Update Operations

**Before:**
```typescript
const treatment = await prisma.treatment.update({
  where: { id: 123 },
  data: { status: "COMPLETED" }
})
```

**After:**
```typescript
const treatment = await prisma.treatment.update({
  where: { 
    id: 123,
    tenantId: currentTenantId // Tenant isolation
  },
  data: { 
    status: "COMPLETED",
    updatedById: currentUserId // Track who updated
  }
})

// Or use Prisma extensions with enhanced functionality:
const treatment = await prisma.treatment.updateTreatmentStatus(123, "COMPLETED", currentUserId)
```

### 4. Use Prisma Extensions

**Extensions provide enhanced functionality:**
```typescript
// Extensions are automatically available on the Prisma client
const treatments = await prisma.treatment.findByStatus("PENDING", currentTenantId)

// Enhanced methods with built-in tenant isolation
const activePatients = await prisma.patient.getActivePatients(currentTenantId)

// Advanced search capabilities
const searchResults = await prisma.patient.searchPatients({
  searchTerm: "John",
  status: "ACTIVE",
  tenantId: currentTenantId
})
```

## Breaking Changes

### 1. PaymentApplication Relations

**Before:**
```typescript
// PaymentApplication didn't have tenant relation
const app = await prisma.paymentApplication.findFirst({
  where: { id: 123 }
})
```

**After:**
```typescript
// Now includes tenant relation
const app = await prisma.paymentApplication.findFirst({
  where: { id: 123, tenantId: currentTenantId },
  include: { tenant: true }
})
```

### 2. User Model Relations

New relations added to User model:
- `createdTreatments` / `updatedTreatments`
- `createdInvoices` / `updatedInvoices`  
- `createdPayments` / `updatedPayments`
- `createdPaymentApplications` / `updatedPaymentApplications`

## Benefits

### 1. Consistency
- All models follow the same pattern
- Predictable field names and types
- Consistent indexing strategy

### 2. Multi-tenancy
- Built-in tenant isolation
- Performance optimized with proper indexes
- Type-safe tenant filtering

### 3. Audit Trail
- Track who created/updated every record
- Timestamp all changes
- Support for system-generated records

### 4. Developer Experience
- TypeScript utilities for type safety
- Prisma extensions provide enhanced functionality
- Consistent API patterns

### 5. Performance
- Optimized indexes for common queries
- Efficient tenant-based filtering
- Reduced query complexity

## Testing Your Changes

### 1. Verify Tenant Isolation
```typescript
// Should only return records for the specified tenant
const records = await prisma.patient.getActivePatients("tenant-1")
```

### 2. Verify Audit Tracking
```typescript
// Should populate audit fields
const record = await prisma.patient.createPatient({
  ...data,
  tenantId: "tenant-1",
  createdById: userId
})
expect(record.createdById).toBe(userId)
expect(record.createdAt).toBeDefined()
```

### 3. Verify Relations
```typescript
// Should include audit relations
const record = await prisma.patient.findUnique({
  where: { id },
  include: { createdBy: true, updatedBy: true }
})
expect(record.createdBy).toBeDefined()
```

## Next Steps

1. **Update your existing code** to use tenant isolation
2. **Use Prisma extensions** for enhanced functionality
3. **Use TypeScript utilities** for type safety
4. **Add proper error handling** for tenant mismatches
5. **Consider implementing soft deletes** using the same pattern

For detailed examples, see the extension implementations in `/lib/prisma-extensions/`.
