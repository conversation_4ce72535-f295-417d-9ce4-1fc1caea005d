# Common Fields Pattern for Prisma Models

## Overview

This document defines the standard pattern for common fields that should be included in all applicable Prisma models. Since Prisma doesn't support abstract models or inheritance like Django, we use a consistent pattern across all models.

## Standard Common Fields Pattern

Every model (except `Tenant` which is the root) should include these common fields:

### 1. Primary Key
```prisma
id        Int      @id @default(autoincrement())
```

### 2. Tenant Relation (Multi-tenancy)
```prisma
tenantId  String
tenant    Tenant   @relation(fields: [tenantId], references: [id])
```

### 3. Audit Timestamps
```prisma
createdAt DateTime @default(now())
updatedAt DateTime @updatedAt
```

### 4. Audit User Relations
```prisma
createdById Int?
updatedById Int?
```

Note: The actual User relation fields are defined per model based on the specific relations needed.

## Complete Pattern Template

```prisma
model ExampleModel {
  // 1. Primary Key
  id        Int      @id @default(autoincrement())
  
  // 2. Tenant Relation
  tenantId  String
  tenant    Tenant   @relation(fields: [tenantId], references: [id])
  
  // 3. Model-specific fields go here
  // ...
  
  // 4. Audit Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // 5. Audit User Relations (optional - only if needed)
  createdById Int?
  updatedById Int?
  
  // 6. Model-specific relations go here
  // ...
  
  // 7. Indexes (always include tenant-based indexes)
  @@index([tenantId, /* other relevant fields */])
}
```

## Current Model Status

### ✅ Models Following Pattern
- `User` - Has all common fields
- `Patient` - Has all common fields
- `Appointment` - Has all common fields with proper User relations
- `CaseSheet` - Has all common fields
- `Tooth` - Has all common fields
- `Finding` - Has all common fields
- `Diagnosis` - Has all common fields
- `Treatment` - Has all common fields (missing User relations)
- `Invoice` - Has all common fields (missing User relations)
- `Payment` - Has all common fields (missing User relations)
- `PaymentApplication` - Has all common fields (missing User relations)
- `Adjustment` - Has all common fields with proper User relations

### ⚠️ Models Needing Updates
- `Treatment` - Missing User relation fields for createdBy/updatedBy
- `Invoice` - Missing User relation fields for createdBy/updatedBy
- `Payment` - Missing User relation fields for createdBy/updatedBy
- `PaymentApplication` - Missing User relation fields for createdBy/updatedBy

### ✅ Special Cases
- `Tenant` - Root model, doesn't need tenant relation or audit fields

## User Relation Patterns

Different models use different patterns for User relations based on their needs:

### Pattern 1: Basic Audit Relations
```prisma
createdById Int?
updatedById Int?
createdBy   User? @relation("ModelCreatedBy", fields: [createdById], references: [id])
updatedBy   User? @relation("ModelUpdatedBy", fields: [updatedById], references: [id])
```

### Pattern 2: Extended Relations (like Appointment)
```prisma
createdById Int?
updatedById Int?
createdBy   User? @relation("CreatedBy", fields: [createdById], references: [id])
modifiedBy  User? @relation("ModifiedBy", fields: [updatedById], references: [id])
```

## Index Patterns

Every model should have at least:
```prisma
@@index([tenantId, /* primary business field */])
```

Additional indexes based on common query patterns:
```prisma
@@index([tenantId, status])
@@index([tenantId, createdAt])
```

## Implementation Guidelines

1. **Always include tenant-based indexes** for multi-tenancy performance
2. **Use consistent naming** for audit fields across all models
3. **Make audit user relations optional** (Int?) to handle system-generated records
4. **Use descriptive relation names** to avoid conflicts when a model has multiple User relations
5. **Follow the field order** as shown in the template for consistency

## Benefits

1. **Consistency** - All models follow the same pattern
2. **Multi-tenancy** - Built-in tenant isolation
3. **Audit Trail** - Track who created/updated records and when
4. **Performance** - Consistent indexing strategy
5. **Maintainability** - Easy to understand and modify
