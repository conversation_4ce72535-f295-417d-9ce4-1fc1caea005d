# Product Requirements Document (PRD) - MVP  
_DnD – Dental Clinic Management Platform (Revised for MVP)_

## Executive Summary
DnD MVP is a streamlined dental clinic management platform focused on rapid revenue generation through simplified clinical workflows. The core principle is _"minimum viable complexity"_ - delivering essential patient management, basic clinical charting, and robust invoicing in the shortest development time possible.

**Key MVP Simplifications:**
- Single case sheet per patient (no complex case management)
- Direct finding-to-treatment workflow (no treatment plans)
- Essential clinical data only
- Focus on revenue-generating features

## 1. Product Vision & Goals

| Goal | Success Metric | Timeline |
|------|---------------|----------|
| Fast market entry | MVP deployed in 4 weeks | Q1 2025 |
| Revenue validation | 3 paying clinics within 30 days of launch | Q1 2025 |
| Core workflow efficiency | <60s to record finding and generate invoice | Launch |

## 2. Core MVP Features

### 2.1 Multitenancy & User Management

**Tenant Structure:**
- Each clinic is a separate tenant with isolated data
- Tenant-specific branding (logo, colors)
- Per-tenant configuration (pricing, procedures)

**User Roles:**
- **Admin**: Full system access, user management
- **Dentist**: Clinical access, can create findings and treatments
- **Staff**: Patient management, appointments, invoicing
- **Patient**: View-only access

### 2.2 Patient Management

**Core Features:**
- Basic demographics (name, phone, email, address)
- Simple search by name or phone
- Patient list with status indicators
- Auto-generated patient ID per tenant

**Simplified Data Model:**
```
Patient:
- Basic demographics
- Contact information
- Created/updated timestamps
- Single case sheet (auto-created)
```

### 2.3 Simplified Clinical Workflow

**Case Sheet Structure:**
- **One case sheet per patient** (created automatically)
- **32 teeth** (adult dentition only for MVP)
- **Findings per tooth** (multiple findings allowed)
- **Direct treatment per finding** (no treatment plans)

**Clinical Data Model:**
```
CaseSheet:
- Patient (1:1 relationship)
- Created date
- 32 Tooth records (auto-generated)

Tooth:
- Tooth number (1-32)
- Multiple findings

Finding:
- Tooth reference
- Description (free text)
- Date recorded
- Dentist who recorded
- Associated treatment (optional)

Treatment:
- Finding reference
- Procedure name
- Cost
- Status (pending/completed)
- Date performed
```

**Simplified Workflow:**
1. Open patient → case sheet loads automatically
2. Click tooth → add finding
3. Add treatment directly to finding
4. Generate invoice from completed treatments

### 2.4 Appointments

**Basic Scheduling:**
- Simple calendar view (day/week)
- Appointment slots with patient assignment
- Status: scheduled/completed/cancelled
- Duration and notes

**Essential Features:**
- Create/edit/cancel appointments
- Link to patient record
- Basic conflict detection

### 2.5 Invoicing & Payments

**Invoice Generation:**
- Auto-generate from completed treatments
- Line items with procedure codes and costs
- Tax calculation (configurable per tenant)
- PDF generation

**Payment Tracking:**
- Payment status (unpaid/partial/paid)
- Payment method tracking
- Simple payment recording
- Outstanding balance calculation

**Revenue Features:**
- Basic reporting (daily/monthly revenue)
- Outstanding invoices list
- Payment history per patient

## 3. Technical Architecture (Simplified)

**Stack:**
- **Backend**: Django (Python) with Django REST Framework
- **Frontend**: Svelte with basic routing
- **Database**: SQLite (for MVP speed)
- **Authentication**: Django built-in with role-based permissions

**Key Models:**
```python
# Core models for MVP
Tenant, User, Patient, CaseSheet, Tooth, Finding, Treatment, 
Appointment, Invoice, Payment
```

## 4. User Experience (MVP)

**Navigation:**
- Simple sidebar: Patients, Appointments, Invoices
- Patient detail: Demographics, Case Sheet, Appointments, Invoices
- Minimal clicks to core actions

**Clinical Interface:**
- Basic tooth diagram (clickable)
- Simple forms for findings and treatments
- Immediate invoice generation

## 5. MVP User Flows

### 5.1 Patient Registration & Case Sheet Creation
1. Add patient → demographics form
2. Save → case sheet auto-created with 32 teeth
3. Navigate to case sheet

### 5.2 Clinical Documentation (Simplified)
1. Open patient case sheet
2. Click tooth → finding form opens
3. Enter finding description
4. Add treatment (procedure + cost)
5. Mark treatment as completed
6. Generate invoice immediately

### 5.3 Appointment & Revenue Flow
1. Schedule appointment
2. Complete appointment → mark treatments as done
3. Generate invoice
4. Record payment
5. Update patient balance

## 6. Data Model (MVP)

```python
# Simplified models focusing on core functionality

class Tenant(models.Model):
    name = models.CharField(max_length=100)
    # Basic tenant info only

class Patient(models.Model):
    tenant = models.ForeignKey(Tenant)
    name = models.CharField(max_length=100)
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    # Minimal demographics

class CaseSheet(models.Model):
    patient = models.OneToOneField(Patient)
    created_date = models.DateTimeField(auto_now_add=True)

class Tooth(models.Model):
    case_sheet = models.ForeignKey(CaseSheet)
    tooth_number = models.IntegerField(1, 32)

class Finding(models.Model):
    tooth = models.ForeignKey(Tooth)
    description = models.TextField()
    date_recorded = models.DateTimeField(auto_now_add=True)
    recorded_by = models.ForeignKey(User)

class Treatment(models.Model):
    finding = models.ForeignKey(Finding)
    procedure_name = models.CharField(max_length=100)
    cost = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(choices=['pending', 'completed'])
    date_performed = models.DateTimeField(null=True)

class Invoice(models.Model):
    patient = models.ForeignKey(Patient)
    treatments = models.ManyToManyField(Treatment)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(choices=['unpaid', 'partial', 'paid'])
    created_date = models.DateTimeField(auto_now_add=True)
```

## 7. MVP Acceptance Criteria

| Feature | Acceptance Test |
|---------|----------------|
| Patient Creation | Create patient → case sheet with 32 teeth auto-generated |
| Finding Entry | Click tooth → add finding → save in <30s |
| Treatment Assignment | Add treatment to finding → cost calculated |
| Invoice Generation | Complete treatment → generate invoice → PDF created |
| Payment Recording | Record payment → patient balance updated |
| Multitenancy | Different tenants see only their data |

## 8. MVP Limitations (Future Features)

**Excluded from MVP:**
- Complex treatment planning
- Multiple case sheets per patient
- Advanced reporting and analytics
- File uploads and image management
- Automated notifications
- Advanced scheduling features
- Loyalty programs
- Complex prognosis tracking

## 9. Development Timeline (8 Weeks)

| Week | Deliverable |
|------|-------------|
| 1 | Core models, authentication, multitenancy |
| 2 | Patient management, basic case sheet |
| 3 | Clinical workflow (findings, treatments) |
| 4 | Invoicing, payments, basic reporting |

## 10. Success Metrics

**Technical:**
- All core workflows functional
- <2s page load times
- Zero data leakage between tenants

**Business:**
- 3 pilot clinics onboarded
- Average invoice generation time <60s
- Positive user feedback on core workflow

## Conclusion

This MVP version of DnD focuses on the essential revenue-generating features while maintaining the core architecture for future expansion. By simplifying the clinical workflow to a direct finding-to-treatment model and eliminating complex treatment planning, we can deliver a functional product in 4 weeks that validates the core business model.

The simplified approach allows clinics to:
1. Manage patients efficiently
2. Document clinical findings quickly
3. Generate invoices immediately
4. Track payments and revenue

Future versions can add back the complexity of treatment planning, advanced case management, and additional clinical features once the core revenue model is validated.

---

**Document Information:**
- Document Title: DnD MVP Product Requirements Document
- Version: 2.0 (Revised)
- Date: July 15, 2025
- Focus: Minimum Viable Product for rapid market entry
- Classification: Internal Development Document