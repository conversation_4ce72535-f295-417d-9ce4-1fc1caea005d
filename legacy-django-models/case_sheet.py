from django.db import models
from django.utils.translation import gettext_lazy as _
from abstract.models import IntEntity


class CaseSheet(IntEntity):
    """
    Simplified case sheet model with one-to-one relationship to Patient.
    Auto-created when a patient is created to serve as the primary clinical record.
    """
    
    # One-to-one relationship with Patient
    patient = models.OneToOneField(
        'Patient', 
        on_delete=models.CASCADE, 
        related_name='case_sheet',
        verbose_name=_('Patient')
    )
    
    # Essential clinical notes
    clinical_notes = models.TextField(_('Clinical Notes'), blank=True)
    
    # Current status
    STATUS_CHOICES = [
        ('ACTIVE', _('Active')),
        ('INACTIVE', _('Inactive')),
    ]
    status = models.CharField(
        _('Status'), 
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='ACTIVE'
    )
    
    # Last updated information
    last_visit_date = models.DateTimeField(_('Last Visit Date'), null=True, blank=True)
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Case Sheet')
        verbose_name_plural = _('Case Sheets')
        ordering = ['-last_visit_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['patient']),
            models.Index(fields=['status']),
            models.Index(fields=['last_visit_date']),
        ]
    
    def __str__(self):
        return f"Case Sheet - {self.patient.full_name}"