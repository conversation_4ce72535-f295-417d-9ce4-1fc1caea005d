from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db.models.signals import post_save
from django.dispatch import receiver
from django_lifecycle import hook, BEFORE_SAVE
from abstract.models import IntEntity
from .case_sheet import CaseSheet


class Tooth(IntEntity):
    """
    Simplified tooth model implementing FDI two-digit notation system.
    Focuses on essential fields for clinical workflow simplification.
    
    FDI System for Adults: Quadrants 1-4 (11-18, 21-28, 31-38, 41-48)
    """
    
    case_sheet = models.ForeignKey(
        CaseSheet,
        on_delete=models.CASCADE,
        related_name='teeth',
        verbose_name=_('Case Sheet')
    )
    
    # FDI notation - only adult teeth (11-48)
    tooth_number = models.PositiveIntegerField(
        _('Tooth Number'),
        validators=[MinValueValidator(11), MaxValueValidator(48)],
        help_text=_('FDI two-digit tooth notation for adult teeth (11-48)')
    )
    
    # Simplified tooth status
    TOOTH_STATUS_CHOICES = [
        ('PRESENT', _('Present')),
        ('MISSING', _('Missing')),
        ('EXTRACTED', _('Extracted')),
        ('CROWN', _('Crown')),
        ('FILLING', _('Filling')),
    ]
    status = models.CharField(
        _('Tooth Status'),
        max_length=20,
        choices=TOOTH_STATUS_CHOICES,
        default='PRESENT'
    )
    
    # Auto-populated from tooth_number
    quadrant = models.PositiveIntegerField(
        _('Quadrant'),
        help_text=_('Auto-populated from tooth number')
    )
    
    position_in_quadrant = models.PositiveIntegerField(
        _('Position in Quadrant'),
        validators=[MinValueValidator(1), MaxValueValidator(8)],
        help_text=_('Auto-populated from tooth number')
    )
    
    # Auto-populated tooth name
    tooth_name = models.CharField(
        _('Tooth Name'),
        max_length=100,
        help_text=_('Auto-populated anatomical name')
    )
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Tooth')
        verbose_name_plural = _('Teeth')
        ordering = ['tooth_number']
        default_permissions = {}
        indexes = [
            models.Index(fields=['case_sheet', 'tooth_number']),
            models.Index(fields=['tooth_number']),
            models.Index(fields=['status']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['case_sheet', 'tooth_number'],
                name='unique_tooth_per_case_sheet'
            )
        ]
    
    def __str__(self):
        return f"Tooth {self.tooth_number} - {self.case_sheet.patient.full_name}"
    
    def get_tooth_name(self):
        """Return anatomical name of the tooth"""
        tooth_names = {
            # Permanent teeth - Adult dentition only
            11: 'Upper Right Central Incisor', 12: 'Upper Right Lateral Incisor',
            13: 'Upper Right Canine', 14: 'Upper Right First Premolar',
            15: 'Upper Right Second Premolar', 16: 'Upper Right First Molar',
            17: 'Upper Right Second Molar', 18: 'Upper Right Third Molar',
            
            21: 'Upper Left Central Incisor', 22: 'Upper Left Lateral Incisor',
            23: 'Upper Left Canine', 24: 'Upper Left First Premolar',
            25: 'Upper Left Second Premolar', 26: 'Upper Left First Molar',
            27: 'Upper Left Second Molar', 28: 'Upper Left Third Molar',
            
            31: 'Lower Left Central Incisor', 32: 'Lower Left Lateral Incisor',
            33: 'Lower Left Canine', 34: 'Lower Left First Premolar',
            35: 'Lower Left Second Premolar', 36: 'Lower Left First Molar',
            37: 'Lower Left Second Molar', 38: 'Lower Left Third Molar',
            
            41: 'Lower Right Central Incisor', 42: 'Lower Right Lateral Incisor',
            43: 'Lower Right Canine', 44: 'Lower Right First Premolar',
            45: 'Lower Right Second Premolar', 46: 'Lower Right First Molar',
            47: 'Lower Right Second Molar', 48: 'Lower Right Third Molar',
        }
        return tooth_names.get(self.tooth_number, f'Tooth {self.tooth_number}')
    
    @hook(BEFORE_SAVE)
    def populate_tooth_data_from_fdi(self):
        """Auto-populate quadrant, position, and tooth name from FDI number"""
        if self.tooth_number:
            self.quadrant = int(str(self.tooth_number)[0])
            self.position_in_quadrant = int(str(self.tooth_number)[1])
            self.tooth_name = self.get_tooth_name()


# Signal to auto-create 32 adult teeth when case sheet is created
@receiver(post_save, sender=CaseSheet)
def create_teeth_for_case_sheet(sender, instance, created, **kwargs):
    """
    Auto-create 32 adult teeth when a case sheet is created.
    Creates teeth for all 4 quadrants with FDI numbers 11-18, 21-28, 31-38, 41-48.
    """
    if created:
        # Define all 32 adult teeth FDI numbers
        adult_teeth = []
        for quadrant in [1, 2, 3, 4]:  # Upper right, upper left, lower left, lower right
            for position in range(1, 9):  # Positions 1-8 in each quadrant
                tooth_number = int(f"{quadrant}{position}")
                adult_teeth.append(tooth_number)
        
        # Create tooth objects and manually populate data (bulk_create bypasses lifecycle hooks)
        teeth_to_create = []
        for tooth_number in adult_teeth:
            # Manually populate fields that would normally be done by the lifecycle hook
            quadrant = int(str(tooth_number)[0])
            position_in_quadrant = int(str(tooth_number)[1])
            
            # Get tooth name from the method
            tooth_names = {
                # Permanent teeth - Adult dentition only
                11: 'Upper Right Central Incisor', 12: 'Upper Right Lateral Incisor',
                13: 'Upper Right Canine', 14: 'Upper Right First Premolar',
                15: 'Upper Right Second Premolar', 16: 'Upper Right First Molar',
                17: 'Upper Right Second Molar', 18: 'Upper Right Third Molar',
                
                21: 'Upper Left Central Incisor', 22: 'Upper Left Lateral Incisor',
                23: 'Upper Left Canine', 24: 'Upper Left First Premolar',
                25: 'Upper Left Second Premolar', 26: 'Upper Left First Molar',
                27: 'Upper Left Second Molar', 28: 'Upper Left Third Molar',
                
                31: 'Lower Left Central Incisor', 32: 'Lower Left Lateral Incisor',
                33: 'Lower Left Canine', 34: 'Lower Left First Premolar',
                35: 'Lower Left Second Premolar', 36: 'Lower Left First Molar',
                37: 'Lower Left Second Molar', 38: 'Lower Left Third Molar',
                
                41: 'Lower Right Central Incisor', 42: 'Lower Right Lateral Incisor',
                43: 'Lower Right Canine', 44: 'Lower Right First Premolar',
                45: 'Lower Right Second Premolar', 46: 'Lower Right First Molar',
                47: 'Lower Right Second Molar', 48: 'Lower Right Third Molar',
            }
            tooth_name = tooth_names.get(tooth_number, f'Tooth {tooth_number}')
            
            tooth = Tooth(
                case_sheet=instance,
                tooth_number=tooth_number,
                status='PRESENT',
                quadrant=quadrant,
                position_in_quadrant=position_in_quadrant,
                tooth_name=tooth_name
            )
            teeth_to_create.append(tooth)
        
        # Bulk create all teeth
        Tooth.objects.bulk_create(teeth_to_create) 