from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator
from django_lifecycle import hook, BEFORE_SAVE
from abstract.models import IntEntity
from .finding import Finding


class Treatment(IntEntity):
    """
    Direct Treatment model linked to findings.
    Eliminates complex treatment planning and provides direct finding-to-treatment mapping.
    """
    
    finding = models.ForeignKey(
        Finding,
        on_delete=models.CASCADE,
        related_name='treatments',
        verbose_name=_('Finding'),
        help_text=_('Clinical finding this treatment addresses')
    )
    
    # Predefined procedure code and name fields
    procedure_code = models.CharField(
        _('Procedure Code'),
        max_length=20,
        help_text=_('Standard procedure code (e.g., ADA codes)')
    )
    
    procedure_name = models.CharField(
        _('Procedure Name'),
        max_length=200,
        help_text=_('Descriptive name of the procedure')
    )
    
    # Manual cost entry field
    cost = models.DecimalField(
        _('Cost'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        help_text=_('Treatment cost (manually entered)')
    )
    
    # Status tracking
    STATUS_CHOICES = [
        ('PLANNED', _('Planned')),
        ('IN_PROGRESS', _('In Progress')),
        ('COMPLETED', _('Completed')),
        ('CANCELLED', _('Cancelled')),
        ('ON_HOLD', _('On Hold')),
    ]
    status = models.CharField(
        _('Status'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='PLANNED',
        help_text=_('Current status of the treatment')
    )
    
    # Completion date and provider fields
    completed_date = models.DateTimeField(
        _('Completed Date'),
        null=True,
        blank=True,
        help_text=_('Date and time when treatment was completed')
    )
    
    completed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='completed_treatments',
        verbose_name=_('Completed By'),
        help_text=_('Provider who completed this treatment')
    )
    
    # Additional tracking fields
    planned_date = models.DateTimeField(
        _('Planned Date'),
        null=True,
        blank=True,
        help_text=_('Planned date for treatment')
    )
    
    assigned_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_treatments',
        verbose_name=_('Assigned To'),
        help_text=_('Provider assigned to perform this treatment')
    )
    
    # Optional notes for treatment-specific information
    notes = models.TextField(
        _('Treatment Notes'),
        blank=True,
        max_length=1000,
        help_text=_('Additional notes about the treatment (max 1000 characters)')
    )
    
    # Priority level based on finding severity
    PRIORITY_CHOICES = [
        ('LOW', _('Low')),
        ('MEDIUM', _('Medium')),
        ('HIGH', _('High')),
        ('URGENT', _('Urgent')),
    ]
    priority = models.CharField(
        _('Priority'),
        max_length=20,
        choices=PRIORITY_CHOICES,
        default='MEDIUM',
        help_text=_('Treatment priority level')
    )
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Treatment')
        verbose_name_plural = _('Treatments')
        ordering = ['-created_at']
        default_permissions = {}
        indexes = [
            models.Index(fields=['finding', '-created_at']),
            models.Index(fields=['status']),
            models.Index(fields=['priority']),
            models.Index(fields=['procedure_code']),
            models.Index(fields=['completed_date']),
            models.Index(fields=['planned_date']),
            models.Index(fields=['assigned_to']),
            models.Index(fields=['completed_by']),
        ]
    
    def __str__(self):
        return f"{self.procedure_name} - {self.finding.tooth.fdi_number} ({self.get_status_display()})"
    
    @property
    def is_completed(self):
        """Check if treatment is completed"""
        return self.status == 'COMPLETED'
    
    @property
    def is_billable(self):
        """Check if treatment can be billed (completed treatments)"""
        return self.status == 'COMPLETED' and self.cost > 0
    
    @property
    def patient(self):
        """Get patient through finding relationship"""
        return self.finding.tooth.case_sheet.patient
    
    @property
    def tooth(self):
        """Get tooth through finding relationship"""
        return self.finding.tooth
    
    @property
    def days_since_planned(self):
        """Calculate days since treatment was planned"""
        if self.planned_date:
            from django.utils import timezone
            return (timezone.now() - self.planned_date).days
        return None
    
    @property
    def is_overdue(self):
        """Check if planned treatment is overdue (more than 30 days)"""
        days_since = self.days_since_planned
        return days_since is not None and days_since > 30 and self.status == 'PLANNED'
    
    def mark_completed(self, completed_by=None, completed_date=None):
        """Mark treatment as completed with optional provider and date"""
        from django.utils import timezone
        
        self.status = 'COMPLETED'
        self.completed_date = completed_date or timezone.now()
        if completed_by:
            self.completed_by = completed_by
        self.save()
    
    def mark_cancelled(self, reason=None):
        """Mark treatment as cancelled with optional reason"""
        self.status = 'CANCELLED'
        if reason:
            self.notes = f"{self.notes}\nCancellation reason: {reason}".strip()
        self.save()
    
    @hook(BEFORE_SAVE)
    def auto_set_priority_from_finding(self):
        """Automatically set priority based on finding severity and prognosis"""
        if self.finding_id:
            finding = self.finding
            if finding.severity == 'EXTENSIVE' or finding.prognosis == 'HOPELESS':
                self.priority = 'URGENT'
            elif finding.severity == 'SEVERE' or finding.prognosis == 'POOR':
                self.priority = 'HIGH'
            elif finding.severity == 'MODERATE':
                self.priority = 'MEDIUM'
            else:
                self.priority = 'LOW'
    
    @classmethod
    def get_procedures_for_finding_category(cls, category):
        """Get suggested procedures for a finding category"""
        procedure_map = {
            'CARIES': [
                ('D2140', 'Amalgam Filling - 1 Surface'),
                ('D2150', 'Amalgam Filling - 2 Surfaces'),
                ('D2160', 'Amalgam Filling - 3 Surfaces'),
                ('D2330', 'Composite Filling - 1 Surface'),
                ('D2331', 'Composite Filling - 2 Surfaces'),
                ('D2332', 'Composite Filling - 3 Surfaces'),
                ('D2740', 'Crown - Porcelain/Ceramic'),
                ('D2750', 'Crown - Porcelain Fused to Metal'),
            ],
            'PERIODONTAL': [
                ('D4210', 'Gingivectomy - Per Quadrant'),
                ('D4240', 'Gingival Flap Procedure'),
                ('D4260', 'Osseous Surgery - Per Quadrant'),
                ('D4341', 'Scaling and Root Planing - Per Quadrant'),
                ('D4355', 'Full Mouth Debridement'),
            ],
            'ENDODONTIC': [
                ('D3310', 'Root Canal - Anterior'),
                ('D3320', 'Root Canal - Bicuspid'),
                ('D3330', 'Root Canal - Molar'),
                ('D3410', 'Apicoectomy - Anterior'),
                ('D3421', 'Apicoectomy - Bicuspid'),
                ('D3425', 'Apicoectomy - Molar'),
            ],
            'ORAL_SURGERY': [
                ('D7140', 'Simple Extraction'),
                ('D7210', 'Surgical Extraction'),
                ('D7220', 'Removal of Impacted Tooth - Soft Tissue'),
                ('D7230', 'Removal of Impacted Tooth - Partial Bony'),
                ('D7240', 'Removal of Impacted Tooth - Complete Bony'),
            ],
            'PROSTHODONTIC': [
                ('D2740', 'Crown - Porcelain/Ceramic'),
                ('D2750', 'Crown - Porcelain Fused to Metal'),
                ('D6240', 'Pontic - Porcelain Fused to Metal'),
                ('D6750', 'Crown - Porcelain Fused to Metal (Bridge)'),
                ('D5110', 'Complete Denture - Upper'),
                ('D5120', 'Complete Denture - Lower'),
                ('D5213', 'Partial Denture - Upper'),
                ('D5214', 'Partial Denture - Lower'),
            ],
            'PREVENTIVE': [
                ('D1110', 'Prophylaxis - Adult'),
                ('D1120', 'Prophylaxis - Child'),
                ('D1206', 'Fluoride Varnish'),
                ('D1208', 'Topical Fluoride'),
                ('D1351', 'Sealant - Per Tooth'),
            ],
            'ORTHODONTIC': [
                ('D8080', 'Comprehensive Orthodontic Treatment'),
                ('D8090', 'Comprehensive Orthodontic Treatment - Adolescent'),
                ('D8210', 'Removable Appliance Therapy'),
                ('D8220', 'Fixed Appliance Therapy'),
            ],
            'ORAL_PATHOLOGY': [
                ('D7286', 'Biopsy of Oral Tissue - Hard'),
                ('D7287', 'Biopsy of Oral Tissue - Soft'),
                ('D7410', 'Excision of Benign Lesion'),
                ('D7440', 'Excision of Malignant Lesion'),
            ],
            'TMJ': [
                ('D7880', 'Occlusal Orthotic Device'),
                ('D7899', 'Unspecified TMJ Therapy'),
                ('D9940', 'Occlusal Guard - Hard Appliance'),
                ('D9944', 'Occlusal Guard - Soft Appliance'),
            ],
            'TRAUMA': [
                ('D7140', 'Simple Extraction'),
                ('D2740', 'Crown - Porcelain/Ceramic'),
                ('D2750', 'Crown - Porcelain Fused to Metal'),
                ('D7210', 'Surgical Extraction'),
                ('D5281', 'Removable Unilateral Partial Denture'),
            ],
            'CONGENITAL': [
                ('D7140', 'Simple Extraction'),
                ('D7210', 'Surgical Extraction'),
                ('D8080', 'Comprehensive Orthodontic Treatment'),
                ('D2740', 'Crown - Porcelain/Ceramic'),
            ],
            'OTHER': [
                ('D0150', 'Comprehensive Oral Evaluation'),
                ('D0274', 'Bitewing Radiographs'),
                ('D0330', 'Panoramic Radiograph'),
                ('D9110', 'Palliative Treatment'),
            ],
        }
        return procedure_map.get(category, [])
    
    @classmethod
    def get_common_procedures(cls):
        """Get most commonly used procedures across all categories"""
        return [
            ('D1110', 'Prophylaxis - Adult'),
            ('D2330', 'Composite Filling - 1 Surface'),
            ('D2331', 'Composite Filling - 2 Surfaces'),
            ('D2740', 'Crown - Porcelain/Ceramic'),
            ('D3310', 'Root Canal - Anterior'),
            ('D3320', 'Root Canal - Bicuspid'),
            ('D3330', 'Root Canal - Molar'),
            ('D4341', 'Scaling and Root Planing - Per Quadrant'),
            ('D7140', 'Simple Extraction'),
            ('D7210', 'Surgical Extraction'),
        ]