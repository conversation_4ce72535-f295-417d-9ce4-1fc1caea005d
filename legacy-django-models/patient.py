from django.db import models
from django.utils.translation import gettext_lazy as _
from django_lifecycle import hook, BEFORE_CREATE, AFTER_CREATE
from abstract.models import IntEntity
from django.conf import settings


class Patient(IntEntity):
    """
    Simplified Patient model for managing essential patient demographics.
    Focuses on core information needed for clinical workflow.
    """

    # User account relationship - patients can login with phone number
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="patient_profile",
        help_text=_("Associated user account for patient authentication"),
    )

    # Core Demographics - Essential fields only
    clinic_id = models.Char<PERSON>ield(
        _("Clinic ID"),
        max_length=20,
        unique=True,
        help_text=_("Auto-generated unique clinic ID"),
    )
    first_name = models.CharField(_("First Name"), max_length=100)
    last_name = models.CharField(_("Last Name"), max_length=100)
    date_of_birth = models.DateField(_("Date of Birth"), null=True, blank=True)

    # Contact Information - Essential only
    phone_number = models.CharField(_("Phone Number"), max_length=20, blank=True)
    email = models.EmailField(_("Email"), blank=True)

    # Single address field instead of multiple components
    address = models.TextField(_("Address"), blank=True)

    # Simplified status choices
    PATIENT_STATUS_CHOICES = [
        ("ACTIVE", _("Active")),
        ("INACTIVE", _("Inactive")),
    ]
    status = models.CharField(
        _("Patient Status"),
        max_length=20,
        choices=PATIENT_STATUS_CHOICES,
        default="ACTIVE",
    )

    class Meta(IntEntity.Meta):
        verbose_name = _("Patient")
        verbose_name_plural = _("Patients")
        ordering = ["last_name", "first_name"]
        default_permissions = {}
        indexes = [
            models.Index(fields=["clinic_id"]),
            models.Index(fields=["last_name", "first_name"]),
            models.Index(fields=["phone_number"]),
            models.Index(fields=["email"]),
            models.Index(fields=["status"]),
        ]

    def __str__(self):
        return f"{self.last_name}, {self.first_name} ({self.clinic_id})"

    @property
    def full_name(self):
        """Return the patient's full name"""
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self):
        """Calculate patient's age"""
        if self.date_of_birth:
            from datetime import date

            today = date.today()
            return (
                today.year
                - self.date_of_birth.year
                - (
                    (today.month, today.day)
                    < (self.date_of_birth.month, self.date_of_birth.day)
                )
            )
        return None

    @hook(BEFORE_CREATE)
    def generate_clinic_id(self):
        """Generate clinic ID if not provided"""
        if not self.clinic_id:
            # Generate a unique clinic ID
            import random
            import string

            while True:
                clinic_id = "P" + "".join(random.choices(string.digits, k=6))
                if not Patient.objects.filter(clinic_id=clinic_id).exists():
                    self.clinic_id = clinic_id
                    break

    @hook(AFTER_CREATE)
    def create_case_sheet(self):
        """Automatically create a CaseSheet when a Patient is created"""
        from .case_sheet import CaseSheet
        CaseSheet.objects.create(patient=self)
