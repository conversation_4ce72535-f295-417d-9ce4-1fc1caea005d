from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django_lifecycle import hook, BEFORE_SAVE
from abstract.models import IntEntity
from .tooth import Tooth


class Finding(IntEntity):
    """
    Simplified Finding model for clinical documentation.
    Focuses on predefined choices to minimize text input and speed up documentation.
    """
    
    tooth = models.ForeignKey(
        Tooth,
        on_delete=models.CASCADE,
        related_name='findings',
        verbose_name=_('Tooth')
    )
    
    # Use existing diagnosis categories from current models
    CATEGORY_CHOICES = [
        ('CARIES', _('Dental Caries')),
        ('PERIODONTAL', _('Periodontal Disease')),
        ('ENDODONTIC', _('Endodontic')),
        ('ORAL_PATHOLOGY', _('Oral Pathology')),
        ('ORTHODONTIC', _('Orthodontic')),
        ('ORAL_SURGERY', _('Oral Surgery')),
        ('PROSTHODONTIC', _('Prosthodontic')),
        ('PREVENTIVE', _('Preventive')),
        ('TMJ', _('TMJ Disorders')),
        ('TRAUMA', _('Trauma')),
        ('CONGENITAL', _('Congenital Anomalies')),
        ('OTHER', _('Other')),
    ]
    category = models.CharField(
        _('Finding Category'),
        max_length=20,
        choices=CATEGORY_CHOICES,
        help_text=_('Primary category of the clinical finding')
    )
    
    # Subcategory based on main category - will be populated based on category selection
    subcategory = models.CharField(
        _('Subcategory'),
        max_length=100,
        help_text=_('Specific subcategory within the main category')
    )
    
    # Use existing severity choices from diagnosis model
    SEVERITY_CHOICES = [
        ('MILD', _('Mild')),
        ('MODERATE', _('Moderate')),
        ('SEVERE', _('Severe')),
        ('EXTENSIVE', _('Extensive')),
    ]
    severity = models.CharField(
        _('Severity'),
        max_length=20,
        choices=SEVERITY_CHOICES,
        default='MILD',
        help_text=_('Severity level of the finding')
    )
    
    # Use existing prognosis choices from diagnosis model
    PROGNOSIS_CHOICES = [
        ('GOOD', _('Good')),
        ('FAIR', _('Fair')),
        ('POOR', _('Poor')),
        ('HOPELESS', _('Hopeless')),
    ]
    prognosis = models.CharField(
        _('Prognosis'),
        max_length=20,
        choices=PROGNOSIS_CHOICES,
        default='GOOD',
        help_text=_('Expected outcome and prognosis')
    )
    
    # Minimal notes field (limited to 500 characters to encourage brevity)
    notes = models.TextField(
        _('Notes'),
        blank=True,
        max_length=500,
        help_text=_('Additional notes (max 500 characters)')
    )
    
    # Provider tracking
    recorded_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='recorded_findings',
        verbose_name=_('Recorded By'),
        help_text=_('Provider who recorded this finding')
    )
    
    recorded_date = models.DateTimeField(
        _('Recorded Date'),
        auto_now_add=True,
        help_text=_('Date and time when finding was recorded')
    )
    
    # Status for tracking finding lifecycle
    STATUS_CHOICES = [
        ('ACTIVE', _('Active')),
        ('RESOLVED', _('Resolved')),
        ('MONITORING', _('Under Monitoring')),
    ]
    status = models.CharField(
        _('Status'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='ACTIVE',
        help_text=_('Current status of the finding')
    )
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Finding')
        verbose_name_plural = _('Findings')
        ordering = ['-recorded_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['tooth', '-recorded_date']),
            models.Index(fields=['category']),
            models.Index(fields=['severity']),
            models.Index(fields=['prognosis']),
            models.Index(fields=['status']),
            models.Index(fields=['recorded_by']),
        ]
    
    def __str__(self):
        return f"{self.get_category_display()} - {self.subcategory} (Tooth {self.tooth.fdi_number})"
    
    @property
    def is_urgent(self):
        """Check if finding requires urgent attention"""
        return self.severity in ['SEVERE', 'EXTENSIVE'] or self.prognosis == 'HOPELESS'
    
    @property
    def priority_level(self):
        """Return priority level based on severity and prognosis"""
        if self.prognosis == 'HOPELESS' or self.severity == 'EXTENSIVE':
            return 'HIGH'
        elif self.severity == 'SEVERE' or self.prognosis == 'POOR':
            return 'MEDIUM'
        else:
            return 'LOW'
    
    @property
    def color_code(self):
        """Return color code for UI display based on priority"""
        priority_colors = {
            'HIGH': 'red',
            'MEDIUM': 'orange',
            'LOW': 'green',
        }
        return priority_colors.get(self.priority_level, 'gray')
    
    def get_suggested_treatments(self):
        """Get suggested treatments based on finding category and subcategory"""
        # This will be implemented when Treatment model is created
        # For now, return empty list
        return []
    
    @classmethod
    def get_subcategories_for_category(cls, category):
        """Return available subcategories for a given category"""
        subcategory_map = {
            'CARIES': [
                'Small Caries',
                'Medium Caries',
                'Large Caries',
                'Root Caries',
                'Recurrent Caries',
            ],
            'PERIODONTAL': [
                'Gingivitis',
                'Mild Periodontitis',
                'Moderate Periodontitis',
                'Severe Periodontitis',
                'Localized Periodontitis',
                'Generalized Periodontitis',
            ],
            'ENDODONTIC': [
                'Pulpitis',
                'Necrotic Pulp',
                'Periapical Lesion',
                'Root Canal Needed',
                'Apical Periodontitis',
            ],
            'ORAL_PATHOLOGY': [
                'Oral Ulcer',
                'Oral Lesion',
                'Suspicious Lesion',
                'Benign Growth',
                'Inflammatory Condition',
            ],
            'ORTHODONTIC': [
                'Crowding',
                'Spacing',
                'Malocclusion',
                'Crossbite',
                'Overbite',
                'Underbite',
            ],
            'ORAL_SURGERY': [
                'Impacted Tooth',
                'Extraction Needed',
                'Surgical Exposure',
                'Cyst',
                'Abscess',
            ],
            'PROSTHODONTIC': [
                'Missing Tooth',
                'Failed Restoration',
                'Crown Needed',
                'Bridge Needed',
                'Denture Issue',
            ],
            'PREVENTIVE': [
                'Plaque Buildup',
                'Calculus',
                'Staining',
                'Fluoride Deficiency',
                'Oral Hygiene Issue',
            ],
            'TMJ': [
                'TMJ Pain',
                'Clicking',
                'Limited Opening',
                'Muscle Tension',
                'Joint Dysfunction',
            ],
            'TRAUMA': [
                'Fractured Tooth',
                'Luxated Tooth',
                'Avulsed Tooth',
                'Soft Tissue Injury',
                'Jaw Injury',
            ],
            'CONGENITAL': [
                'Missing Tooth',
                'Extra Tooth',
                'Malformed Tooth',
                'Enamel Defect',
                'Developmental Anomaly',
            ],
            'OTHER': [
                'Unspecified Finding',
                'Multiple Conditions',
                'Complex Case',
                'Requires Further Evaluation',
            ],
        }
        return subcategory_map.get(category, [])