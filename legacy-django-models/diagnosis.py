from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django_lifecycle import hook, BEFORE_SAVE
from abstract.models import IntEntity
from .finding import Finding


class Diagnosis(IntEntity):
    """
    Simplified Diagnosis model that references Finding instead of complex relationships.
    Focuses on essential fields: category, severity, prognosis, status for MVP.
    """
    
    finding = models.ForeignKey(
        Finding,
        on_delete=models.CASCADE,
        related_name='diagnoses',
        verbose_name=_('Finding'),
        help_text=_('Clinical finding this diagnosis is based on')
    )
    
    diagnosis_date = models.DateTimeField(
        _('Diagnosis Date'),
        auto_now_add=True,
        help_text=_('Date and time when diagnosis was made')
    )
    
    # Use same category choices as Finding model for consistency
    DIAGNOSIS_CATEGORY_CHOICES = [
        ('CARIES', _('Dental Caries')),
        ('PERIODONTAL', _('Periodontal Disease')),
        ('ENDODONTIC', _('Endodontic')),
        ('ORAL_PATHOLOGY', _('Oral Pathology')),
        ('ORTHODONTIC', _('Orthodontic')),
        ('ORAL_SURGERY', _('Oral Surgery')),
        ('PROSTHODONTIC', _('Prosthodontic')),
        ('PREVENTIVE', _('Preventive')),
        ('TMJ', _('TMJ Disorders')),
        ('TRAUMA', _('Trauma')),
        ('CONGENITAL', _('Congenital Anomalies')),
        ('OTHER', _('Other')),
    ]
    category = models.CharField(
        _('Diagnosis Category'),
        max_length=20,
        choices=DIAGNOSIS_CATEGORY_CHOICES,
        help_text=_('Primary category of the diagnosis')
    )
    
    # Simplified severity choices
    SEVERITY_CHOICES = [
        ('MILD', _('Mild')),
        ('MODERATE', _('Moderate')),
        ('SEVERE', _('Severe')),
        ('EXTENSIVE', _('Extensive')),
    ]
    severity = models.CharField(
        _('Severity'),
        max_length=20,
        choices=SEVERITY_CHOICES,
        help_text=_('Severity level of the diagnosis')
    )
    
    # Simplified prognosis choices
    PROGNOSIS_CHOICES = [
        ('GOOD', _('Good')),
        ('FAIR', _('Fair')),
        ('POOR', _('Poor')),
        ('HOPELESS', _('Hopeless')),
    ]
    prognosis = models.CharField(
        _('Prognosis'),
        max_length=20,
        choices=PROGNOSIS_CHOICES,
        help_text=_('Expected outcome and prognosis')
    )
    
    # Simplified status choices
    DIAGNOSIS_STATUS_CHOICES = [
        ('ACTIVE', _('Active')),
        ('STABLE', _('Stable')),
        ('RESOLVED', _('Resolved')),
        ('CHRONIC', _('Chronic')),
        ('RECURRENT', _('Recurrent')),
    ]
    status = models.CharField(
        _('Diagnosis Status'),
        max_length=20,
        choices=DIAGNOSIS_STATUS_CHOICES,
        default='ACTIVE',
        help_text=_('Current status of the diagnosis')
    )
    
    # Provider information
    diagnosing_provider = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='made_diagnoses',
        verbose_name=_('Diagnosing Provider'),
        help_text=_('Provider who made this diagnosis')
    )
    
    # Minimal notes field
    notes = models.TextField(
        _('Additional Notes'),
        blank=True,
        max_length=500,
        help_text=_('Additional notes (max 500 characters)')
    )
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Diagnosis')
        verbose_name_plural = _('Diagnoses')
        ordering = ['-diagnosis_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['finding', '-diagnosis_date']),
            models.Index(fields=['category']),
            models.Index(fields=['severity']),
            models.Index(fields=['prognosis']),
            models.Index(fields=['status']),
            models.Index(fields=['diagnosing_provider']),
        ]
    
    def __str__(self):
        return f"{self.get_category_display()} - {self.finding.subcategory} (Tooth {self.finding.tooth.tooth_number})"
    
    @property
    def patient(self):
        """Get patient through finding relationship"""
        return self.finding.tooth.case_sheet.patient
    
    @property
    def tooth(self):
        """Get tooth through finding relationship"""
        return self.finding.tooth
    
    @property
    def is_periodontal(self):
        """Check if diagnosis is periodontal-related"""
        return self.category == 'PERIODONTAL'
    
    @property
    def is_urgent(self):
        """Check if diagnosis requires urgent attention"""
        return self.prognosis == 'HOPELESS' or self.severity in ['SEVERE', 'EXTENSIVE']
    
    @property
    def priority_level(self):
        """Return priority level based on severity and prognosis"""
        if self.prognosis == 'HOPELESS' or self.severity == 'EXTENSIVE':
            return 'HIGH'
        elif self.severity == 'SEVERE' or self.prognosis == 'POOR':
            return 'MEDIUM'
        else:
            return 'LOW'
    
    @property
    def prognosis_color_code(self):
        """Return color code for prognosis display"""
        color_map = {
            'GOOD': 'green',
            'FAIR': 'amber',
            'POOR': 'orange',
            'HOPELESS': 'red',
        }
        return color_map.get(self.prognosis, 'gray')
    
    def get_similar_diagnoses(self):
        """Get similar diagnoses based on category and severity"""
        return Diagnosis.objects.filter(
            category=self.category,
            severity=self.severity
        ).exclude(id=self.id)[:5]
    