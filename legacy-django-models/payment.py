from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django_lifecycle import BEFORE_CREATE, BEFORE_SAVE, hook
from abstract.models import IntEntity
from .patient import Patient
from .invoice import Invoice


class Payment(IntEntity):
    """
    Payment model for tracking financial transactions.
    Supports multiple payment methods and integrates with ledger view.
    """
    
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='payments',
        verbose_name=_('Patient')
    )
    
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='payments',
        verbose_name=_('Invoice'),
        help_text=_('Invoice this payment is applied to')
    )
    
    payment_number = models.CharField(
        _('Payment Number'),
        max_length=20,
        help_text=_('Auto-generated payment number')
    )
    
    payment_date = models.DateTimeField(_('Payment Date'))
    
    # Payment amounts
    amount = models.DecimalField(
        _('Payment Amount'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0.01)]
    )
    
    applied_amount = models.DecimalField(
        _('Applied Amount'),
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text=_('Amount applied to invoices')
    )
    
    unapplied_amount = models.DecimalField(
        _('Unapplied Amount'),
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text=_('Amount not yet applied to invoices')
    )
    
    # Payment method and processing
    PAYMENT_METHOD_CHOICES = [
        ('CASH', _('Cash')),
        ('CHECK', _('Check')),
        ('CREDIT_CARD', _('Credit Card')),
        ('DEBIT_CARD', _('Debit Card')),
        ('BANK_TRANSFER', _('Bank Transfer')),
        ('ACH', _('ACH/Electronic Check')),
        ('PAYPAL', _('PayPal')),
        ('VENMO', _('Venmo')),
        ('APPLE_PAY', _('Apple Pay')),
        ('GOOGLE_PAY', _('Google Pay')),
        ('INSURANCE', _('Insurance Payment')),
        ('CARE_CREDIT', _('CareCredit')),
        ('PAYMENT_PLAN', _('Payment Plan')),
        ('GIFT_CARD', _('Gift Card')),
        ('LOYALTY_POINTS', _('Loyalty Points')),
        ('OTHER', _('Other')),
    ]
    payment_method = models.CharField(
        _('Payment Method'),
        max_length=20,
        choices=PAYMENT_METHOD_CHOICES
    )
    
    # Payment status
    PAYMENT_STATUS_CHOICES = [
        ('PENDING', _('Pending')),
        ('PROCESSING', _('Processing')),
        ('COMPLETED', _('Completed')),
        ('FAILED', _('Failed')),
        ('CANCELLED', _('Cancelled')),
        ('REFUNDED', _('Refunded')),
        ('DISPUTED', _('Disputed')),
        ('CHARGEBACK', _('Chargeback')),
    ]
    status = models.CharField(
        _('Payment Status'),
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='COMPLETED'
    )
    
    # Payment processing details
    transaction_id = models.CharField(
        _('Transaction ID'),
        max_length=100,
        blank=True,
        help_text=_('External transaction ID from payment processor')
    )
    
    processor_response = models.TextField(
        _('Processor Response'),
        blank=True,
        help_text=_('Response from payment processor')
    )
    
    # Check-specific fields
    check_number = models.CharField(
        _('Check Number'),
        max_length=50,
        blank=True
    )
    
    bank_name = models.CharField(
        _('Bank Name'),
        max_length=200,
        blank=True
    )
    
    # Card-specific fields
    card_last_four = models.CharField(
        _('Card Last 4 Digits'),
        max_length=4,
        blank=True
    )
    
    CARD_TYPE_CHOICES = [
        ('VISA', _('Visa')),
        ('MASTERCARD', _('Mastercard')),
        ('AMEX', _('American Express')),
        ('DISCOVER', _('Discover')),
        ('OTHER', _('Other')),
    ]
    card_type = models.CharField(
        _('Card Type'),
        max_length=20,
        choices=CARD_TYPE_CHOICES,
        blank=True
    )
    
    # Processing fees
    processing_fee = models.DecimalField(
        _('Processing Fee'),
        max_digits=8,
        decimal_places=2,
        default=0.00
    )
    
    net_amount = models.DecimalField(
        _('Net Amount'),
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text=_('Payment amount minus processing fees')
    )
    
    # Payment source/channel
    PAYMENT_SOURCE_CHOICES = [
        ('FRONT_DESK', _('Front Desk')),
        ('ONLINE', _('Online Payment')),
        ('PHONE', _('Phone Payment')),
        ('MAIL', _('Mail')),
        ('MOBILE_APP', _('Mobile App')),
        ('TEXT_TO_PAY', _('Text-to-Pay')),  # As mentioned in PRD
        ('AUTO_PAY', _('Auto-pay')),
        ('KIOSK', _('Self-service Kiosk')),
    ]
    payment_source = models.CharField(
        _('Payment Source'),
        max_length=20,
        choices=PAYMENT_SOURCE_CHOICES,
        default='FRONT_DESK'
    )
    
    # Staff and administrative
    received_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='received_payments',
        verbose_name=_('Received By')
    )
    
    processed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_payments',
        verbose_name=_('Processed By')
    )
    
    # Reconciliation and deposits
    is_deposited = models.BooleanField(_('Deposited'), default=False)
    deposit_date = models.DateField(_('Deposit Date'), null=True, blank=True)
    deposit_batch = models.CharField(
        _('Deposit Batch'),
        max_length=50,
        blank=True
    )
    
    # Refund handling
    is_refunded = models.BooleanField(_('Refunded'), default=False)
    refund_date = models.DateTimeField(_('Refund Date'), null=True, blank=True)
    refund_amount = models.DecimalField(
        _('Refund Amount'),
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    refund_reason = models.TextField(_('Refund Reason'), blank=True)
    
    # Notes and references
    notes = models.TextField(_('Notes'), blank=True)
    internal_notes = models.TextField(_('Internal Notes'), blank=True)
    
    # Patient receipt
    receipt_sent = models.BooleanField(_('Receipt Sent'), default=False)
    receipt_email = models.EmailField(_('Receipt Email'), blank=True)
    receipt_method = models.CharField(
        _('Receipt Method'),
        max_length=20,
        choices=[
            ('EMAIL', _('Email')),
            ('SMS', _('SMS')),
            ('PRINT', _('Print')),
            ('NONE', _('No Receipt')),
        ],
        default='EMAIL'
    )
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')
        ordering = ['-payment_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['patient', '-payment_date']),
            models.Index(fields=['payment_number']),
            models.Index(fields=['status']),
            models.Index(fields=['payment_method']),
            models.Index(fields=['invoice']),
            models.Index(fields=['transaction_id']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['payment_number', 'ten'],
                name='unique_payment_number_per_tenant'
            )
        ]
    
    def __str__(self):
        return f"Payment {self.payment_number} - {self.patient.full_name} (${self.amount})"
    
    @property
    def is_successful(self):
        """Check if payment was successful"""
        return self.status == 'COMPLETED'
    
    @property
    def requires_deposit(self):
        """Check if payment requires deposit"""
        deposit_methods = ['CASH', 'CHECK']
        return (
            self.payment_method in deposit_methods and 
            not self.is_deposited and 
            self.is_successful
        )
    
    @property
    def is_electronic(self):
        """Check if payment is electronic"""
        electronic_methods = [
            'CREDIT_CARD', 'DEBIT_CARD', 'BANK_TRANSFER', 'ACH',
            'PAYPAL', 'VENMO', 'APPLE_PAY', 'GOOGLE_PAY'
        ]
        return self.payment_method in electronic_methods
    
    @property
    def payment_method_display_name(self):
        """Return user-friendly payment method name"""
        if self.payment_method == 'CREDIT_CARD' and self.card_type:
            return f"{self.get_card_type_display()} ending in {self.card_last_four}"
        elif self.payment_method == 'CHECK' and self.check_number:
            return f"Check #{self.check_number}"
        return self.get_payment_method_display()
    
    @property
    def status_color(self):
        """Return color code for status display"""
        color_map = {
            'PENDING': 'blue',
            'PROCESSING': 'orange',
            'COMPLETED': 'green',
            'FAILED': 'red',
            'CANCELLED': 'gray',
            'REFUNDED': 'orange',
            'DISPUTED': 'red',
            'CHARGEBACK': 'red',
        }
        return color_map.get(self.status, 'gray')
    
    def apply_to_invoice(self, invoice=None, amount=None):
        """Apply payment to an invoice"""
        if invoice is None:
            invoice = self.invoice
        
        if invoice and amount is None:
            amount = min(self.unapplied_amount, invoice.balance_due)
        
        if invoice and amount > 0:
            # Update invoice
            invoice.amount_paid += amount
            invoice.balance_due = max(0, invoice.balance_due - amount)
            
            # Update invoice status
            if invoice.balance_due <= 0:
                invoice.status = 'PAID'
            elif invoice.amount_paid > 0:
                invoice.status = 'PARTIAL_PAYMENT'
            
            invoice.save()
            
            # Update payment allocation
            self.applied_amount += amount
            self.unapplied_amount -= amount
            
            # Create payment application record
            PaymentApplication.objects.create(
                payment=self,
                invoice=invoice,
                amount=amount
            )
            
            return True
        return False
    
    def process_refund(self, amount, reason=''):
        """Process a refund for this payment"""
        if amount <= self.amount and not self.is_refunded:
            self.refund_amount = amount
            self.refund_reason = reason
            self.is_refunded = True
            from django.utils import timezone
            self.refund_date = timezone.now()
            
            if amount == self.amount:
                self.status = 'REFUNDED'
            
            # Update patient account balance
            self.patient.account_balance += amount
            self.patient.save()
            
            return True
        return False
    
    @hook(BEFORE_CREATE)
    def generate_payment_number(self):
        """Generate payment number if not provided"""
        if not self.payment_number:
            from datetime import datetime
            import random
            import string
            
            date_str = datetime.now().strftime('%Y%m%d')
            while True:
                random_suffix = ''.join(random.choices(string.digits, k=3))
                payment_number = f"PAY{date_str}{random_suffix}"
                if not Payment.objects.filter(payment_number=payment_number).exists():
                    self.payment_number = payment_number
                    break
    
    @hook(BEFORE_SAVE)
    def calculate_amounts(self):
        """Calculate net amount and initialize unapplied amount"""
        # Calculate net amount
        self.net_amount = self.amount - self.processing_fee
        
        # Initialize unapplied amount if not set
        if self.unapplied_amount == 0 and self.applied_amount == 0:
            self.unapplied_amount = self.amount


class PaymentApplication(IntEntity):
    """
    Track how payments are applied to invoices.
    Supports the ledger view for charges, payments, adjustments.
    """
    
    payment = models.ForeignKey(
        Payment,
        on_delete=models.CASCADE,
        related_name='applications',
        verbose_name=_('Payment')
    )
    
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.CASCADE,
        related_name='payment_applications',
        verbose_name=_('Invoice')
    )
    
    amount = models.DecimalField(
        _('Applied Amount'),
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0.01)]
    )
    
    application_date = models.DateTimeField(_('Application Date'))
    
    # Administrative
    applied_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='applied_payments',
        verbose_name=_('Applied By')
    )
    
    notes = models.TextField(_('Notes'), blank=True)
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Payment Application')
        verbose_name_plural = _('Payment Applications')
        ordering = ['-application_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['payment', '-application_date']),
            models.Index(fields=['invoice', '-application_date']),
        ]
    
    def __str__(self):
        return f"{self.payment.payment_number} → {self.invoice.invoice_number} (${self.amount})"


class Adjustment(IntEntity):
    """
    Account adjustments for ledger view.
    Supports discounts, write-offs, and other account modifications.
    """
    
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='adjustments',
        verbose_name=_('Patient')
    )
    
    invoice = models.ForeignKey(
        Invoice,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='adjustments',
        verbose_name=_('Invoice')
    )
    
    adjustment_number = models.CharField(
        _('Adjustment Number'),
        max_length=20,
        help_text=_('Auto-generated adjustment number')
    )
    
    adjustment_date = models.DateTimeField(_('Adjustment Date'))
    
    # Adjustment type and amount
    ADJUSTMENT_TYPE_CHOICES = [
        ('DISCOUNT', _('Discount')),
        ('WRITE_OFF', _('Write Off')),
        ('COURTESY_ADJUSTMENT', _('Courtesy Adjustment')),
        ('INSURANCE_ADJUSTMENT', _('Insurance Adjustment')),
        ('PROMPT_PAY_DISCOUNT', _('Prompt Pay Discount')),
        ('FAMILY_DISCOUNT', _('Family Discount')),
        ('SENIOR_DISCOUNT', _('Senior Discount')),
        ('HARDSHIP_ADJUSTMENT', _('Hardship Adjustment')),
        ('BILLING_ERROR', _('Billing Error Correction')),
        ('OTHER', _('Other')),
    ]
    adjustment_type = models.CharField(
        _('Adjustment Type'),
        max_length=30,
        choices=ADJUSTMENT_TYPE_CHOICES
    )
    
    amount = models.DecimalField(
        _('Adjustment Amount'),
        max_digits=10,
        decimal_places=2,
        help_text=_('Positive for credits, negative for charges')
    )
    
    # Reason and approval
    reason = models.TextField(_('Reason'), help_text=_('Reason for adjustment'))
    
    approved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_adjustments',
        verbose_name=_('Approved By')
    )
    
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_adjustments',
        verbose_name=_('Created By')
    )
    
    # Status
    ADJUSTMENT_STATUS_CHOICES = [
        ('PENDING', _('Pending Approval')),
        ('APPROVED', _('Approved')),
        ('DENIED', _('Denied')),
        ('APPLIED', _('Applied')),
    ]
    status = models.CharField(
        _('Status'),
        max_length=20,
        choices=ADJUSTMENT_STATUS_CHOICES,
        default='PENDING'
    )
    
    notes = models.TextField(_('Notes'), blank=True)
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Adjustment')
        verbose_name_plural = _('Adjustments')
        ordering = ['-adjustment_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['patient', '-adjustment_date']),
            models.Index(fields=['adjustment_type']),
            models.Index(fields=['status']),
            models.Index(fields=['invoice']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['adjustment_number', 'ten'],
                name='unique_adjustment_number_per_tenant'
            )
        ]
    
    def __str__(self):
        return f"Adjustment {self.adjustment_number} - {self.patient.full_name} (${self.amount})"
    
    @property
    def is_credit(self):
        """Check if adjustment is a credit (positive amount)"""
        return self.amount > 0
    
    @property
    def is_charge(self):
        """Check if adjustment is a charge (negative amount)"""
        return self.amount < 0
    
    @hook(BEFORE_CREATE)
    def generate_adjustment_number(self):
        """Auto-generate adjustment number"""
        if not self.adjustment_number:
            from datetime import datetime
            import random
            import string
            
            date_str = datetime.now().strftime('%Y%m%d')
            while True:
                random_suffix = ''.join(random.choices(string.digits, k=3))
                adjustment_number = f"ADJ{date_str}{random_suffix}"
                if not Adjustment.objects.filter(adjustment_number=adjustment_number).exists():
                    self.adjustment_number = adjustment_number
                    break 