from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django_lifecycle import BEFORE_CREATE, BEFORE_SAVE, hook
from abstract.models import IntEntity
from .patient import Patient


class Invoice(IntEntity):
    """
    Invoice model supporting one-click invoice generation from accepted treatment plans.
    Includes discounts, promo codes, membership tiers, and loyalty balance integration.
    """
    
    patient = models.ForeignKey(
        Patient,
        on_delete=models.CASCADE,
        related_name='invoices',
        verbose_name=_('Patient')
    )

    
    invoice_number = models.CharField(
        _('Invoice Number'),
        max_length=20,
        help_text=_('Auto-generated invoice number')
    )
    
    invoice_date = models.DateTimeField(_('Invoice Date'))
    
    # Invoice status and workflow
    INVOICE_STATUS_CHOICES = [
        ('DRAFT', _('Draft')),
        ('PENDING', _('Pending')),
        ('SENT', _('Sent to Patient')),
        ('VIEWED', _('Viewed by Patient')),
        ('PARTIAL_PAYMENT', _('Partial Payment')),
        ('PAID', _('Paid in Full')),
        ('OVERDUE', _('Overdue')),
        ('CANCELLED', _('Cancelled')),
        ('REFUNDED', _('Refunded')),
        ('WRITE_OFF', _('Write Off')),
    ]
    status = models.CharField(
        _('Invoice Status'),
        max_length=20,
        choices=INVOICE_STATUS_CHOICES,
        default='DRAFT'
    )
    
    # Financial totals
    subtotal = models.DecimalField(
        _('Subtotal'),
        max_digits=12,
        decimal_places=2,
        default=0.00
    )
    
    total_amount = models.DecimalField(
        _('Total Amount'),
        max_digits=12,
        decimal_places=2,
        default=0.00
    )
    
    amount_paid = models.DecimalField(
        _('Amount Paid'),
        max_digits=12,
        decimal_places=2,
        default=0.00
    )
    
    balance_due = models.DecimalField(
        _('Balance Due'),
        max_digits=12,
        decimal_places=2,
        default=0.00
    )
    
    # Payment terms and due dates
    payment_terms_days = models.PositiveIntegerField(
        _('Payment Terms (Days)'),
        default=30,
        help_text=_('Number of days until payment is due')
    )
    
    due_date = models.DateField(_('Due Date'))
    
    # Text-to-pay functionality (as mentioned in PRD)
    text_to_pay_sent = models.BooleanField(_('Text-to-Pay Sent'), default=False)
    text_to_pay_link = models.URLField(_('Text-to-Pay Link'), blank=True)
    
    class Meta(IntEntity.Meta):
        verbose_name = _('Invoice')
        verbose_name_plural = _('Invoices')
        ordering = ['-invoice_date']
        default_permissions = {}
        indexes = [
            models.Index(fields=['patient', '-invoice_date']),
            models.Index(fields=['invoice_number']),
            models.Index(fields=['status']),
            models.Index(fields=['due_date']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['invoice_number', 'ten'],
                name='unique_invoice_number_per_tenant'
            )
        ]
    
    def __str__(self):
        return f"Invoice {self.invoice_number} - {self.patient.full_name} (${self.total_amount})"
    
    @hook(BEFORE_CREATE)
    def generate_invoice_number(self):
        """Generate invoice number if not provided"""
        if not self.invoice_number:
            from datetime import datetime
            import random
            import string
            
            date_str = datetime.now().strftime('%Y%m%d')
            while True:
                random_suffix = ''.join(random.choices(string.digits, k=3))
                invoice_number = f"INV{date_str}{random_suffix}"
                if not Invoice.objects.filter(invoice_number=invoice_number).exists():
                    self.invoice_number = invoice_number
                    break
    
    @hook(BEFORE_SAVE)
    def calculate_due_date(self):
        """Auto-calculate due date"""
        if not self.due_date and self.invoice_date:
            from datetime import timedelta
            self.due_date = self.invoice_date.date() + timedelta(days=self.payment_terms_days)