import { describe, it, expect, beforeEach } from 'vitest';
import { prisma } from '@/lib/prisma';
import { TenantContextManager } from '@/lib/tenant-context';
import { setCurrentUserId, getCurrentUserId, AuditUtils } from '@/lib/prisma-extensions/audit-fields';
import { TestDataFactory } from '../setup';

describe('Audit Fields Extension', () => {
  let tenant: any;
  let adminUser: any;
  let staffUser: any;

  beforeEach(async () => {
    tenant = await TestDataFactory.createTestTenant();
    TenantContextManager.setGlobalContext({ tenantId: tenant.id });

    // Create admin and staff users for audit testing
    adminUser = await TestDataFactory.createTestUser(tenant.id, {
      userType: 'ADMIN',
      firstName: 'Admin',
      lastName: 'User'
    });

    staffUser = await TestDataFactory.createTestUser(tenant.id, {
      userType: 'STAFF',
      firstName: 'Staff',
      lastName: 'User'
    });
  });

  describe('Audit Field Injection', () => {
    it('should automatically inject audit fields on create', async () => {
      setCurrentUserId(adminUser.id);

      const patient = await prisma.patient.create({
        data: {
          userId: staffUser.id,
          firstName: 'Test',
          lastName: 'Patient',
          phoneNumber: '+**********',
          dateOfBirth: new Date('1990-01-01'),
        }
      });

      expect(patient.createdById).toBe(adminUser.id);
      expect(patient.updatedById).toBe(adminUser.id);
      expect(patient.createdAt).toBeInstanceOf(Date);
      expect(patient.updatedAt).toBeInstanceOf(Date);
      expect(patient.createdAt.getTime()).toBeLessThanOrEqual(patient.updatedAt.getTime());
    });

    it('should automatically inject audit fields on createMany', async () => {
      setCurrentUserId(adminUser.id);

      const result = await prisma.patient.createMany({
        data: [
          {
            userId: staffUser.id,
            firstName: 'Patient1',
            lastName: 'Test',
            phoneNumber: '+**********',
            dateOfBirth: new Date('1990-01-01'),
          },
          {
            userId: staffUser.id,
            firstName: 'Patient2',
            lastName: 'Test',
            phoneNumber: '+**********',
            dateOfBirth: new Date('1991-01-01'),
          }
        ]
      });

      expect(result.count).toBe(2);

      const patients = await prisma.patient.findMany({
        orderBy: { firstName: 'asc' }
      });

      expect(patients).toHaveLength(2);
      patients.forEach(patient => {
        expect(patient.createdById).toBe(adminUser.id);
        expect(patient.updatedById).toBe(adminUser.id);
        expect(patient.createdAt).toBeInstanceOf(Date);
        expect(patient.updatedAt).toBeInstanceOf(Date);
      });
    });

    it('should automatically inject audit fields on update', async () => {
      setCurrentUserId(adminUser.id);

      // Create patient
      const patient = await prisma.patient.create({
        data: {
          userId: staffUser.id,
          firstName: 'Test',
          lastName: 'Patient',
          phoneNumber: '+**********',
          dateOfBirth: new Date('1990-01-01'),
        }
      });

      const originalUpdatedAt = patient.updatedAt;

      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));

      // Update with different user
      setCurrentUserId(staffUser.id);

      const updatedPatient = await prisma.patient.update({
        where: { id: patient.id },
        data: { firstName: 'Updated' }
      });

      expect(updatedPatient.createdById).toBe(adminUser.id); // Should remain unchanged
      expect(updatedPatient.updatedById).toBe(staffUser.id); // Should be updated
      expect(updatedPatient.createdAt).toEqual(patient.createdAt); // Should remain unchanged
      expect(updatedPatient.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });

    it('should automatically inject audit fields on updateMany', async () => {
      setCurrentUserId(adminUser.id);

      // Create multiple patients
      await prisma.patient.createMany({
        data: [
          {
            userId: staffUser.id,
            firstName: 'Patient1',
            lastName: 'Test',
            phoneNumber: '+**********',
            dateOfBirth: new Date('1990-01-01'),
          },
          {
            userId: staffUser.id,
            firstName: 'Patient2',
            lastName: 'Test',
            phoneNumber: '+**********',
            dateOfBirth: new Date('1991-01-01'),
          }
        ]
      });

      // Update with different user
      setCurrentUserId(staffUser.id);

      const result = await prisma.patient.updateMany({
        where: { lastName: 'Test' },
        data: { lastName: 'Updated' }
      });

      expect(result.count).toBe(2);

      const patients = await prisma.patient.findMany({
        where: { lastName: 'Updated' }
      });

      patients.forEach(patient => {
        expect(patient.createdById).toBe(adminUser.id); // Should remain unchanged
        expect(patient.updatedById).toBe(staffUser.id); // Should be updated
        expect(patient.lastName).toBe('Updated');
      });
    });

    it('should automatically inject audit fields on upsert', async () => {
      setCurrentUserId(adminUser.id);

      // Test upsert create path
      const patient1 = await prisma.patient.upsert({
        where: { id: 999999 }, // Non-existent ID
        create: {
          userId: staffUser.id,
          firstName: 'New',
          lastName: 'Patient',
          phoneNumber: '+**********',
          dateOfBirth: new Date('1990-01-01'),
        },
        update: {
          firstName: 'Updated'
        }
      });

      expect(patient1.createdById).toBe(adminUser.id);
      expect(patient1.updatedById).toBe(adminUser.id);
      expect(patient1.firstName).toBe('New');

      // Test upsert update path
      setCurrentUserId(staffUser.id);

      const patient2 = await prisma.patient.upsert({
        where: { id: patient1.id },
        create: {
          userId: staffUser.id,
          firstName: 'New2',
          lastName: 'Patient2',
          phoneNumber: '+**********',
          dateOfBirth: new Date('1990-01-01'),
        },
        update: {
          firstName: 'Updated'
        }
      });

      expect(patient2.id).toBe(patient1.id);
      expect(patient2.createdById).toBe(adminUser.id); // Should remain unchanged
      expect(patient2.updatedById).toBe(staffUser.id); // Should be updated
      expect(patient2.firstName).toBe('Updated');
    });

    it('should handle explicit audit field values', async () => {
      setCurrentUserId(adminUser.id);

      // Create with explicit createdById
      const patient = await prisma.patient.create({
        data: {
          userId: staffUser.id,
          firstName: 'Test',
          lastName: 'Patient',
          phoneNumber: '+**********',
          dateOfBirth: new Date('1990-01-01'),
          createdById: staffUser.id, // Explicit value should take precedence
        }
      });

      expect(patient.createdById).toBe(staffUser.id);
      expect(patient.updatedById).toBe(staffUser.id);
    });

    it('should not inject audit fields for models without audit fields', async () => {
      setCurrentUserId(adminUser.id);

      // Tenant model doesn't have audit fields
      const tenant = await prisma.tenant.create({
        data: {
          id: 'test-tenant-no-audit'
        }
      });

      // Should not have audit fields
      expect(tenant).not.toHaveProperty('createdById');
      expect(tenant).not.toHaveProperty('updatedById');
      expect(tenant).not.toHaveProperty('createdAt');
      expect(tenant).not.toHaveProperty('updatedAt');
    });
  });

  describe('Current User ID Management', () => {
    it('should get and set current user ID', () => {
      expect(getCurrentUserId()).toBeNull();

      setCurrentUserId(adminUser.id);
      expect(getCurrentUserId()).toBe(adminUser.id);

      setCurrentUserId(staffUser.id);
      expect(getCurrentUserId()).toBe(staffUser.id);

      setCurrentUserId(null);
      expect(getCurrentUserId()).toBeNull();
    });

    it('should handle operations without current user ID', async () => {
      setCurrentUserId(null);

      const patient = await prisma.patient.create({
        data: {
          userId: staffUser.id,
          firstName: 'Test',
          lastName: 'Patient',
          phoneNumber: '+**********',
          dateOfBirth: new Date('1990-01-01'),
        }
      });

      expect(patient.createdById).toBeNull();
      expect(patient.updatedById).toBeNull();
      expect(patient.createdAt).toBeInstanceOf(Date);
      expect(patient.updatedAt).toBeInstanceOf(Date);
    });
  });

  describe('Audit Utilities', () => {
    it('should create audit fields manually', () => {
      setCurrentUserId(adminUser.id);

      const auditFields = AuditUtils.createAuditFields();

      expect(auditFields.createdById).toBe(adminUser.id);
      expect(auditFields.updatedById).toBe(adminUser.id);
      expect(auditFields.createdAt).toBeInstanceOf(Date);
      expect(auditFields.updatedAt).toBeInstanceOf(Date);
    });

    it('should create audit fields with explicit user ID', () => {
      const auditFields = AuditUtils.createAuditFields(staffUser.id);

      expect(auditFields.createdById).toBe(staffUser.id);
      expect(auditFields.updatedById).toBe(staffUser.id);
      expect(auditFields.createdAt).toBeInstanceOf(Date);
      expect(auditFields.updatedAt).toBeInstanceOf(Date);
    });

    it('should create update audit fields', () => {
      setCurrentUserId(staffUser.id);

      const updateFields = AuditUtils.updateAuditFields();

      expect(updateFields.updatedById).toBe(staffUser.id);
      expect(updateFields.updatedAt).toBeInstanceOf(Date);
      expect(updateFields).not.toHaveProperty('createdById');
      expect(updateFields).not.toHaveProperty('createdAt');
    });

    it('should validate audit fields', () => {
      // Valid audit fields
      const validFields = {
        createdById: adminUser.id,
        updatedById: staffUser.id,
        createdAt: new Date(Date.now() - 1000),
        updatedAt: new Date(),
      };

      const validResult = AuditUtils.validateAuditFields(validFields);
      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      // Invalid audit fields - missing createdById
      const invalidFields1 = {
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const invalidResult1 = AuditUtils.validateAuditFields(invalidFields1);
      expect(invalidResult1.isValid).toBe(false);
      expect(invalidResult1.errors).toContain('createdById is required when createdAt is present');

      // Invalid audit fields - createdAt after updatedAt
      const invalidFields2 = {
        createdById: adminUser.id,
        updatedById: staffUser.id,
        createdAt: new Date(),
        updatedAt: new Date(Date.now() - 1000),
      };

      const invalidResult2 = AuditUtils.validateAuditFields(invalidFields2);
      expect(invalidResult2.isValid).toBe(false);
      expect(invalidResult2.errors).toContain('createdAt cannot be after updatedAt');
    });

    it('should get audit trail information', () => {
      const record = {
        createdById: adminUser.id,
        updatedById: staffUser.id,
        createdAt: new Date(Date.now() - 1000),
        updatedAt: new Date(),
      };

      const auditTrail = AuditUtils.getAuditTrail(record);

      expect(auditTrail.created.by).toBe(adminUser.id);
      expect(auditTrail.created.at).toEqual(record.createdAt);
      expect(auditTrail.updated.by).toBe(staffUser.id);
      expect(auditTrail.updated.at).toEqual(record.updatedAt);
    });
  });

  describe('Compliance Requirements', () => {
    it('should maintain audit trail through multiple updates', async () => {
      setCurrentUserId(adminUser.id);

      // Create patient
      const patient = await prisma.patient.create({
        data: {
          userId: staffUser.id,
          firstName: 'Test',
          lastName: 'Patient',
          phoneNumber: '+**********',
          dateOfBirth: new Date('1990-01-01'),
        }
      });

      const originalCreatedAt = patient.createdAt;
      const originalCreatedById = patient.createdById;

      // Multiple updates by different users
      setCurrentUserId(staffUser.id);
      const update1 = await prisma.patient.update({
        where: { id: patient.id },
        data: { firstName: 'Updated1' }
      });

      setCurrentUserId(adminUser.id);
      const update2 = await prisma.patient.update({
        where: { id: patient.id },
        data: { firstName: 'Updated2' }
      });

      // Verify audit trail integrity
      expect(update2.createdAt).toEqual(originalCreatedAt); // Never changes
      expect(update2.createdById).toBe(originalCreatedById); // Never changes
      expect(update2.updatedById).toBe(adminUser.id); // Latest updater
      expect(update2.updatedAt.getTime()).toBeGreaterThan(update1.updatedAt.getTime());
    });

    it('should preserve audit fields across all models with audit support', async () => {
      setCurrentUserId(adminUser.id);

      // Create a complete workflow with audit fields
      const patient = await TestDataFactory.createTestPatient(tenant.id);
      const caseSheet = await prisma.caseSheet.create({
        data: {
          patientId: patient.id,
          clinicalNotes: 'Test complaint'
        }
      });

      const tooth = await prisma.tooth.create({
        data: {
          caseSheetId: caseSheet.id,
          toothNumber: 1,
          quadrant: 1,
          position: 1
        }
      });

      const finding = await TestDataFactory.createTestFinding(tenant.id, tooth.id);
      const treatment = await TestDataFactory.createTestTreatment(tenant.id, finding.id);
      const invoice = await TestDataFactory.createTestInvoice(tenant.id, patient.id);
      const payment = await TestDataFactory.createTestPayment(tenant.id, patient.id, invoice.id);

      // Verify all records have audit fields
      const records = [patient, caseSheet, tooth, finding, treatment, invoice, payment];
      
      records.forEach(record => {
        expect(record.createdById).toBe(adminUser.id);
        expect(record.updatedById).toBe(adminUser.id);
        expect(record.createdAt).toBeInstanceOf(Date);
        expect(record.updatedAt).toBeInstanceOf(Date);
      });
    });
  });
});