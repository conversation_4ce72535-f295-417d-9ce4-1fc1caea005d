import { describe, it, expect, beforeEach } from 'vitest';
import { prisma } from '@/lib/prisma';
import { TenantContextManager } from '@/lib/tenant-context';
import { setCurrentUserId } from '@/lib/prisma-extensions/audit-fields';
import { TestDataFactory } from '../setup';
import * as bcrypt from 'bcryptjs';

describe('User Extension', () => {
  let tenant: any;

  beforeEach(async () => {
    tenant = await TestDataFactory.createTestTenant();
    TenantContextManager.setGlobalContext({ tenantId: tenant.id });
  });

  describe('Simplified User Creation', () => {
    it('should create admin user with username requirement', async () => {
      const adminData = {
        tenantId: tenant.id,
        username: 'admin123',
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Admin',
        lastName: 'User',
      };

      const admin = await prisma.user.createAdminUser(adminData);

      expect(admin.userType).toBe('ADMIN');
      expect(admin.username).toBe('admin123');
      expect(admin.email).toBe('<EMAIL>');
      expect(admin.firstName).toBe('Admin');
      expect(admin.lastName).toBe('User');
      expect(admin.isActive).toBe(true);
      expect(admin.tenantId).toBe(tenant.id);

      // Verify password is hashed
      const isPasswordValid = await bcrypt.compare('password123', admin.password);
      expect(isPasswordValid).toBe(true);
    });

    it('should create staff user with username requirement', async () => {
      const staffData = {
        tenantId: tenant.id,
        username: 'staff123',
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Staff',
        lastName: 'Staff',
      };

      const staff = await prisma.user.createStaffUser(staffData);

      expect(staff.userType).toBe('STAFF');
      expect(staff.username).toBe('staff123');
      expect(staff.phoneNumber).toBe('+**********');
      expect(staff.firstName).toBe('Staff');
      expect(staff.lastName).toBe('Staff');
    });

    it('should create patient user with phone number requirement', async () => {
      const patientData = {
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Patient',
        lastName: 'User',
      };

      const patient = await prisma.user.createPatientUser(patientData);

      expect(patient.userType).toBe('PATIENT');
      expect(patient.phoneNumber).toBe('+**********');
      expect(patient.username).toBe('+**********'); // Should use phone as username
      expect(patient.firstName).toBe('Patient');
      expect(patient.lastName).toBe('User');
    });

    it('should create dentist user with flexible identifier requirement', async () => {
      // Test with username
      const dentistData1 = {
        tenantId: tenant.id,
        username: 'dentist123',
        password: 'password123',
        firstName: 'Dentist',
        lastName: 'User',
      };

      const dentist1 = await prisma.user.createDentistUser(dentistData1);
      expect(dentist1.userType).toBe('DENTIST');
      expect(dentist1.username).toBe('dentist123');

      // Test with phone number
      const dentistData2 = {
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Dentist2',
        lastName: 'User',
      };

      const dentist2 = await prisma.user.createDentistUser(dentistData2);
      expect(dentist2.userType).toBe('DENTIST');
      expect(dentist2.phoneNumber).toBe('+**********');
    });

    it('should enforce validation rules for user creation', async () => {
      // Admin without username should fail
      await expect(
        prisma.user.createAdminUser({
          tenantId: tenant.id,
          password: 'password123',
          firstName: 'Admin',
          lastName: 'User',
        })
      ).rejects.toThrow('Username is required for Admin users');

      // Staff without username should fail
      await expect(
        prisma.user.createStaffUser({
          tenantId: tenant.id,
          password: 'password123',
          firstName: 'Staff',
          lastName: 'User',
        })
      ).rejects.toThrow('Username is required for Staff users');

      // Patient without phone number should fail
      await expect(
        prisma.user.createPatientUser({
          tenantId: tenant.id,
          password: 'password123',
          firstName: 'Patient',
          lastName: 'User',
        })
      ).rejects.toThrow('Phone number is required for Patient users');

      // Dentist without username or phone should fail
      await expect(
        prisma.user.createDentistUser({
          tenantId: tenant.id,
          password: 'password123',
          firstName: 'Dentist',
          lastName: 'User',
        })
      ).rejects.toThrow('Either username or phone number is required for Dentist users');
    });

    it('should normalize email addresses', async () => {
      const userData = {
        tenantId: tenant.id,
        username: 'testuser',
        email: '  <EMAIL>  ',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      const user = await prisma.user.createUser(userData);
      expect(user.email).toBe('<EMAIL>');
    });

    it('should handle generic user creation with default patient type', async () => {
      const userData = {
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Generic',
        lastName: 'User',
      };

      const user = await prisma.user.createUser(userData);
      expect(user.userType).toBe('PATIENT'); // Default type
      expect(user.username).toBe('+**********'); // Phone as username for patients
    });
  });

  describe('Simplified User Type Filtering', () => {
    beforeEach(async () => {
      // Create users of different types
      await prisma.user.createAdminUser({
        tenantId: tenant.id,
        username: 'admin1',
        password: 'password123',
        firstName: 'Admin1',
        lastName: 'User',
      });

      await prisma.user.createStaffUser({
        tenantId: tenant.id,
        username: 'staff1',
        password: 'password123',
        firstName: 'Staff1',
        lastName: 'User',
      });

      await prisma.user.createPatientUser({
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Patient1',
        lastName: 'User',
      });

      await prisma.user.createDentistUser({
        tenantId: tenant.id,
        username: 'dentist1',
        password: 'password123',
        firstName: 'Dentist1',
        lastName: 'User',
      });
    });

    it('should filter users by type', async () => {
      const admins = await prisma.user.getUsersByType('ADMIN', tenant.id);
      expect(admins).toHaveLength(1);
      expect(admins[0].userType).toBe('ADMIN');
      expect(admins[0].firstName).toBe('Admin1');

      const staff = await prisma.user.getUsersByType('STAFF', tenant.id);
      expect(staff).toHaveLength(1);
      expect(staff[0].userType).toBe('STAFF');
      expect(staff[0].firstName).toBe('Staff1');

      const patients = await prisma.user.getUsersByType('PATIENT', tenant.id);
      expect(patients).toHaveLength(1);
      expect(patients[0].userType).toBe('PATIENT');
      expect(patients[0].firstName).toBe('Patient1');

      const dentists = await prisma.user.getUsersByType('DENTIST', tenant.id);
      expect(dentists).toHaveLength(1);
      expect(dentists[0].userType).toBe('DENTIST');
      expect(dentists[0].firstName).toBe('Dentist1');
    });

    it('should order users by name', async () => {
      // Create additional users to test ordering
      await prisma.user.createPatientUser({
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Alice',
        lastName: 'Smith',
      });

      await prisma.user.createPatientUser({
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Bob',
        lastName: 'Johnson',
      });

      const patients = await prisma.user.getUsersByType('PATIENT', tenant.id);
      expect(patients).toHaveLength(3);
      
      // Should be ordered by lastName, then firstName
      expect(patients[0].lastName).toBe('Johnson'); // Bob Johnson
      expect(patients[1].lastName).toBe('Smith');   // Alice Smith
      expect(patients[2].lastName).toBe('User');    // Patient1 User
    });

    it('should only return active users', async () => {
      // Create inactive user
      const inactiveUser = await prisma.user.createPatientUser({
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Inactive',
        lastName: 'User',
        isActive: false,
      });

      const patients = await prisma.user.getUsersByType('PATIENT', tenant.id);
      
      // Should not include inactive user
      const inactiveFound = patients.find(p => p.id === inactiveUser.id);
      expect(inactiveFound).toBeUndefined();
    });
  });

  describe('Simplified Authentication', () => {
    let testUser: any;

    beforeEach(async () => {
      testUser = await prisma.user.createPatientUser({
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      });
    });

    it('should authenticate user with phone number', async () => {
      const authenticatedUser = await prisma.user.authenticate(
        '+**********',
        'password123',
        tenant.id
      );

      expect(authenticatedUser).toBeTruthy();
      expect(authenticatedUser!.id).toBe(testUser.id);
      expect(authenticatedUser!.phoneNumber).toBe('+**********');
    });

    it('should authenticate user with username', async () => {
      const userWithUsername = await prisma.user.createStaffUser({
        tenantId: tenant.id,
        username: 'staffuser',
        password: 'password123',
        firstName: 'Staff',
        lastName: 'User',
      });

      const authenticatedUser = await prisma.user.authenticate(
        'staffuser',
        'password123',
        tenant.id
      );

      expect(authenticatedUser).toBeTruthy();
      expect(authenticatedUser!.id).toBe(userWithUsername.id);
      expect(authenticatedUser!.username).toBe('staffuser');
    });

    it('should return null for invalid credentials', async () => {
      // Wrong password
      const result1 = await prisma.user.authenticate(
        '+**********',
        'wrongpassword',
        tenant.id
      );
      expect(result1).toBeNull();

      // Wrong identifier
      const result2 = await prisma.user.authenticate(
        '+9999999999',
        'password123',
        tenant.id
      );
      expect(result2).toBeNull();

      // Wrong tenant
      const result3 = await prisma.user.authenticate(
        '+**********',
        'password123',
        'wrong-tenant'
      );
      expect(result3).toBeNull();
    });

    it('should not authenticate inactive users', async () => {
      // Deactivate user
      await prisma.user.update({
        where: { id: testUser.id },
        data: { isActive: false }
      });

      const result = await prisma.user.authenticate(
        '+**********',
        'password123',
        tenant.id
      );
      expect(result).toBeNull();
    });
  });

  describe('Password Management', () => {
    let testUser: any;

    beforeEach(async () => {
      testUser = await prisma.user.createPatientUser({
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      });
    });

    it('should change password successfully', async () => {
      const updatedUser = await prisma.user.changePassword(
        testUser.id,
        'password123',
        'newpassword123'
      );

      expect(updatedUser.id).toBe(testUser.id);

      // Verify old password no longer works
      const oldAuth = await prisma.user.authenticate(
        '+**********',
        'password123',
        tenant.id
      );
      expect(oldAuth).toBeNull();

      // Verify new password works
      const newAuth = await prisma.user.authenticate(
        '+**********',
        'newpassword123',
        tenant.id
      );
      expect(newAuth).toBeTruthy();
      expect(newAuth!.id).toBe(testUser.id);
    });

    it('should reject incorrect current password', async () => {
      await expect(
        prisma.user.changePassword(
          testUser.id,
          'wrongpassword',
          'newpassword123'
        )
      ).rejects.toThrow('Current password is incorrect');
    });

    it('should reject short passwords', async () => {
      await expect(
        prisma.user.changePassword(
          testUser.id,
          'password123',
          'short'
        )
      ).rejects.toThrow('Password must be at least 8 characters long');
    });

    it('should reject password change for non-existent user', async () => {
      await expect(
        prisma.user.changePassword(
          999999,
          'password123',
          'newpassword123'
        )
      ).rejects.toThrow('User not found');
    });
  });

  describe('User Type Management', () => {
    let testUser: any;

    beforeEach(async () => {
      testUser = await prisma.user.createPatientUser({
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
      });
    });

    it('should update user type successfully', async () => {
      setCurrentUserId(1); // Set updater

      const updatedUser = await prisma.user.updateUserType(
        testUser.id,
        'STAFF',
        1
      );

      expect(updatedUser.userType).toBe('STAFF');
      expect(updatedUser.updatedById).toBe(1);
    });

    it('should validate user type requirements', async () => {
      // Patient requires phone number (already has it)
      await expect(
        prisma.user.updateUserType(testUser.id, 'PATIENT')
      ).resolves.toBeTruthy();

      // Create user without username
      const userWithoutUsername = await prisma.user.create({
        data: {
          tenantId: tenant.id,
          phoneNumber: '+**********',
          password: 'hashedpassword',
          firstName: 'Test2',
          lastName: 'User2',
          userType: 'PATIENT',
        }
      });

      // Admin requires username (doesn't have it)
      await expect(
        prisma.user.updateUserType(userWithoutUsername.id, 'ADMIN')
      ).rejects.toThrow('Username is required for Admin and Staff users');

      // Staff requires username (doesn't have it)
      await expect(
        prisma.user.updateUserType(userWithoutUsername.id, 'STAFF')
      ).rejects.toThrow('Username is required for Admin and Staff users');
    });

    it('should handle non-existent user', async () => {
      await expect(
        prisma.user.updateUserType(999999, 'ADMIN')
      ).rejects.toThrow('User not found');
    });
  });

  describe('User Lookup Methods', () => {
    let userWithUsername: any;
    let userWithPhone: any;

    beforeEach(async () => {
      userWithUsername = await prisma.user.createStaffUser({
        tenantId: tenant.id,
        username: 'staffuser',
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Staff',
        lastName: 'User',
      });

      userWithPhone = await prisma.user.createPatientUser({
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Patient',
        lastName: 'User',
      });
    });

    it('should find user by username', async () => {
      const foundUser = await prisma.user.findByIdentifier('staffuser', tenant.id);
      
      expect(foundUser).toBeTruthy();
      expect(foundUser!.id).toBe(userWithUsername.id);
      expect(foundUser!.username).toBe('staffuser');
    });

    it('should find user by phone number', async () => {
      const foundUser = await prisma.user.findByIdentifier('+**********', tenant.id);
      
      expect(foundUser).toBeTruthy();
      expect(foundUser!.id).toBe(userWithPhone.id);
      expect(foundUser!.phoneNumber).toBe('+**********');
    });

    it('should return null for non-existent identifier', async () => {
      const foundUser = await prisma.user.findByIdentifier('nonexistent', tenant.id);
      expect(foundUser).toBeNull();
    });

    it('should not find inactive users', async () => {
      // Deactivate user
      await prisma.user.update({
        where: { id: userWithUsername.id },
        data: { isActive: false }
      });

      const foundUser = await prisma.user.findByIdentifier('staffuser', tenant.id);
      expect(foundUser).toBeNull();
    });

    it('should respect tenant isolation', async () => {
      // Create user in different tenant
      const otherTenant = await TestDataFactory.createTestTenant();
      await prisma.user.create({
        data: {
          tenantId: otherTenant.id,
          username: 'othertenant',
          phoneNumber: '+**********',
          password: 'hashedpassword',
          firstName: 'Other',
          lastName: 'User',
          userType: 'PATIENT',
        }
      });

      // Should not find user from other tenant
      const foundUser = await prisma.user.findByIdentifier('othertenant', tenant.id);
      expect(foundUser).toBeNull();
    });
  });

  describe('Computed Properties', () => {
    let users: any[];

    beforeEach(async () => {
      users = await Promise.all([
        prisma.user.createAdminUser({
          tenantId: tenant.id,
          username: 'admin1',
          password: 'password123',
          firstName: 'John',
          lastName: 'Admin',
        }),
        prisma.user.createPatientUser({
          tenantId: tenant.id,
          phoneNumber: '+**********',
          password: 'password123',
          firstName: 'Jane',
          lastName: 'Patient',
        }),
        prisma.user.createStaffUser({
          tenantId: tenant.id,
          username: 'staff1',
          password: 'password123',
          firstName: '',
          lastName: '',
        }),
        prisma.user.createDentistUser({
          tenantId: tenant.id,
          username: 'dentist1',
          password: 'password123',
          firstName: 'Dr. Smith',
          lastName: 'Dentist',
        }),
      ]);
    });

    it('should generate correct display names', async () => {
      const foundUsers = await prisma.user.findMany({
        orderBy: { userType: 'asc' }
      });

      // Admin with full name and username
      expect(foundUsers[0].displayName).toBe('John Admin (admin1)');

      // Dentist with full name and username
      expect(foundUsers[1].displayName).toBe('Dr. Smith Dentist (dentist1)');

      // Patient with full name and phone
      expect(foundUsers[2].displayName).toBe('Jane Patient (+**********)');

      // Staff with no name, fallback to username
      expect(foundUsers[3].displayName).toBe('staff1 (staff1)');
    });

    it('should identify clinical providers correctly', async () => {
      const foundUsers = await prisma.user.findMany({
        orderBy: { userType: 'asc' }
      });

      expect(foundUsers[0].isClinicalProvider).toBe(true);  // ADMIN
      expect(foundUsers[1].isClinicalProvider).toBe(true);  // DENTIST
      expect(foundUsers[2].isClinicalProvider).toBe(false); // PATIENT
      expect(foundUsers[3].isClinicalProvider).toBe(false); // STAFF
    });

    it('should generate correct user type displays', async () => {
      const foundUsers = await prisma.user.findMany({
        orderBy: { userType: 'asc' }
      });

      expect(foundUsers[0].userTypeDisplay).toBe('Admin');
      expect(foundUsers[1].userTypeDisplay).toBe('Dentist');
      expect(foundUsers[2].userTypeDisplay).toBe('Patient');
      expect(foundUsers[3].userTypeDisplay).toBe('Staff');
    });

    it('should return correct primary auth field', async () => {
      const foundUsers = await prisma.user.findMany({
        orderBy: { userType: 'asc' }
      });

      expect(foundUsers[0].primaryAuthField).toBe('admin1');      // ADMIN uses username
      expect(foundUsers[1].primaryAuthField).toBe('dentist1');   // DENTIST uses username
      expect(foundUsers[2].primaryAuthField).toBe('+**********'); // PATIENT uses phone
      expect(foundUsers[3].primaryAuthField).toBe('staff1');     // STAFF uses username
    });
  });
});