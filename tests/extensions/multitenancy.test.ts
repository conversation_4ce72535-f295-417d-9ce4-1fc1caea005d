import { describe, it, expect, beforeEach } from 'vitest';
import { prisma } from '@/lib/prisma';
import { TenantContextManager } from '@/lib/tenant-context';
import { TestDataFactory } from '../setup';

describe('Multitenancy Extension', () => {
  let tenant1: any;
  let tenant2: any;

  beforeEach(async () => {
    // Create test tenants
    tenant1 = await TestDataFactory.createTestTenant({ id: 'tenant-1' });
    tenant2 = await TestDataFactory.createTestTenant({ id: 'tenant-2' });
  });

  describe('Tenant Isolation', () => {
    it('should automatically filter records by tenant on findMany', async () => {
      // Set tenant context
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      // Create users in different tenants
      const user1 = await TestDataFactory.createTestUser(tenant1.id, { firstName: 'User1' });
      const user2 = await TestDataFactory.createTestUser(tenant2.id, { firstName: 'User2' });

      // Query should only return tenant1 users
      const users = await prisma.user.findMany();
      
      expect(users).toHaveLength(1);
      expect(users[0].id).toBe(user1.id);
      expect(users[0].tenantId).toBe(tenant1.id);
    });

    it('should automatically filter records by tenant on findFirst', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const user1 = await TestDataFactory.createTestUser(tenant1.id, { firstName: 'User1' });
      const user2 = await TestDataFactory.createTestUser(tenant2.id, { firstName: 'User2' });

      const user = await prisma.user.findFirst({
        orderBy: { firstName: 'asc' }
      });

      expect(user).toBeTruthy();
      expect(user!.id).toBe(user1.id);
      expect(user!.tenantId).toBe(tenant1.id);
    });

    it('should automatically filter records by tenant on findUnique', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const user1 = await TestDataFactory.createTestUser(tenant1.id);
      const user2 = await TestDataFactory.createTestUser(tenant2.id);

      // Should find user1 since we're in tenant1 context
      const foundUser1 = await prisma.user.findUnique({
        where: { id: user1.id }
      });
      expect(foundUser1).toBeTruthy();
      expect(foundUser1!.id).toBe(user1.id);

      // Should not find user2 since it's in a different tenant
      const foundUser2 = await prisma.user.findUnique({
        where: { id: user2.id }
      });
      expect(foundUser2).toBeNull();
    });

    it('should automatically inject tenant ID on create', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const user = await prisma.user.create({
        data: {
          firstName: 'Test',
          lastName: 'User',
          phoneNumber: '+**********',
          email: '<EMAIL>',
          username: 'testuser',
          password: 'hashedpassword',
          userType: 'PATIENT',
        }
      });

      expect(user.tenantId).toBe(tenant1.id);
    });

    it('should automatically inject tenant ID on createMany', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const result = await prisma.user.createMany({
        data: [
          {
            firstName: 'User1',
            lastName: 'Test',
            phoneNumber: '+**********',
            email: '<EMAIL>',
            username: 'user1',
            password: 'hashedpassword',
            userType: 'PATIENT',
          },
          {
            firstName: 'User2',
            lastName: 'Test',
            phoneNumber: '+**********',
            email: '<EMAIL>',
            username: 'user2',
            password: 'hashedpassword',
            userType: 'STAFF',
          }
        ]
      });

      expect(result.count).toBe(2);

      // Verify both users have correct tenant ID
      const users = await prisma.user.findMany({
        orderBy: { firstName: 'asc' }
      });

      expect(users).toHaveLength(2);
      expect(users[0].tenantId).toBe(tenant1.id);
      expect(users[1].tenantId).toBe(tenant1.id);
    });

    it('should automatically filter on update operations', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const user1 = await TestDataFactory.createTestUser(tenant1.id, { firstName: 'User1' });
      const user2 = await TestDataFactory.createTestUser(tenant2.id, { firstName: 'User2' });

      // Should only update user1 (in current tenant)
      const updatedUser = await prisma.user.update({
        where: { id: user1.id },
        data: { firstName: 'Updated User1' }
      });

      expect(updatedUser.firstName).toBe('Updated User1');

      // Attempting to update user2 should fail (not found in current tenant)
      await expect(
        prisma.user.update({
          where: { id: user2.id },
          data: { firstName: 'Updated User2' }
        })
      ).rejects.toThrow();
    });

    it('should automatically filter on delete operations', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const user1 = await TestDataFactory.createTestUser(tenant1.id);
      const user2 = await TestDataFactory.createTestUser(tenant2.id);

      // Should delete user1 successfully
      const deletedUser = await prisma.user.delete({
        where: { id: user1.id }
      });
      expect(deletedUser.id).toBe(user1.id);

      // Attempting to delete user2 should fail (not found in current tenant)
      await expect(
        prisma.user.delete({
          where: { id: user2.id }
        })
      ).rejects.toThrow();
    });
  });

  describe('Bypass Tenant Functionality', () => {
    it('should allow bypassing tenant filtering for administrative operations', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const user1 = await TestDataFactory.createTestUser(tenant1.id, { firstName: 'User1' });
      const user2 = await TestDataFactory.createTestUser(tenant2.id, { firstName: 'User2' });

      // Normal query should only return tenant1 users
      const normalUsers = await prisma.user.findMany();
      expect(normalUsers).toHaveLength(1);

      // Bypass query should return all users
      const allUsers = await prisma.bypassTenant(async () => {
        return await prisma.user.findMany({
          orderBy: { firstName: 'asc' }
        });
      });

      expect(allUsers).toHaveLength(2);
      expect(allUsers[0].firstName).toBe('User1');
      expect(allUsers[1].firstName).toBe('User2');
    });

    it('should restore tenant context after bypass operation', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const user1 = await TestDataFactory.createTestUser(tenant1.id);
      const user2 = await TestDataFactory.createTestUser(tenant2.id);

      // Verify current tenant context
      expect(prisma.getCurrentTenantId()).toBe(tenant1.id);

      // Perform bypass operation
      await prisma.bypassTenant(async () => {
        expect(prisma.getCurrentTenantId()).toBeNull();
        return await prisma.user.findMany();
      });

      // Verify tenant context is restored
      expect(prisma.getCurrentTenantId()).toBe(tenant1.id);

      // Verify normal filtering still works
      const users = await prisma.user.findMany();
      expect(users).toHaveLength(1);
      expect(users[0].tenantId).toBe(tenant1.id);
    });
  });

  describe('Tenant Context Management', () => {
    it('should return current tenant ID', async () => {
      expect(prisma.getCurrentTenantId()).toBeNull();

      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });
      expect(prisma.getCurrentTenantId()).toBe(tenant1.id);

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      expect(prisma.getCurrentTenantId()).toBe(tenant2.id);

      TenantContextManager.clearGlobalContext();
      expect(prisma.getCurrentTenantId()).toBeNull();
    });

    it('should handle operations without tenant context', async () => {
      // Clear any existing tenant context
      TenantContextManager.clearGlobalContext();

      // Operations should work without tenant filtering when no context is set
      const user = await prisma.user.create({
        data: {
          tenantId: tenant1.id, // Manually specify tenant
          firstName: 'Test',
          lastName: 'User',
          phoneNumber: '+**********',
          email: '<EMAIL>',
          username: 'testuser',
          password: 'hashedpassword',
          userType: 'PATIENT',
        }
      });

      expect(user.tenantId).toBe(tenant1.id);

      // Should find the user without tenant filtering
      const foundUser = await prisma.user.findUnique({
        where: { id: user.id }
      });
      expect(foundUser).toBeTruthy();
    });
  });

  describe('Cross-Model Tenant Isolation', () => {
    it('should maintain tenant isolation across related models', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      // Create patient in tenant1
      const patient1 = await TestDataFactory.createTestPatient(tenant1.id);
      
      // Create patient in tenant2
      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      const patient2 = await TestDataFactory.createTestPatient(tenant2.id);

      // Switch back to tenant1
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      // Create appointments for both patients
      const appointment1 = await TestDataFactory.createTestAppointment(tenant1.id, patient1.id);
      
      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      const appointment2 = await TestDataFactory.createTestAppointment(tenant2.id, patient2.id);

      // Switch back to tenant1 and verify isolation
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const appointments = await prisma.appointment.findMany({
        include: { patient: true }
      });

      expect(appointments).toHaveLength(1);
      expect(appointments[0].id).toBe(appointment1.id);
      expect(appointments[0].patient.id).toBe(patient1.id);
      expect(appointments[0].tenantId).toBe(tenant1.id);
    });
  });
});