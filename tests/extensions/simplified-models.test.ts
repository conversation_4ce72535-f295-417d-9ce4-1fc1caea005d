import { describe, it, expect, beforeEach } from 'vitest';
import { prisma } from '@/lib/prisma';
import { TenantContextManager } from '@/lib/tenant-context';
import { setCurrentUserId } from '@/lib/prisma-extensions/audit-fields';
import { TestDataFactory } from '../setup';

describe('Simplified Models Extensions', () => {
  let tenant: any;
  let dentist: any;
  let patient: any;
  let caseSheet: any;
  let tooth: any;

  beforeEach(async () => {
    tenant = await TestDataFactory.createTestTenant();
    TenantContextManager.setGlobalContext({ tenantId: tenant.id });

    dentist = await prisma.user.createDentistUser({
      tenantId: tenant.id,
      username: 'dentist1',
      password: 'password123',
      firstName: 'Dr. <PERSON>',
      lastName: 'Smith',
    });

    const patientUser = await prisma.user.createPatientUser({
      tenantId: tenant.id,
      phoneNumber: '+**********',
      password: 'password123',
      firstName: 'Alice',
      lastName: '<PERSON>',
    });

    patient = await prisma.patient.create({
      data: {
        tenantId: tenant.id,
        userId: patientUser.id,
        firstName: 'Alice',
        lastName: '<PERSON>',
        phoneNumber: '+**********',
        dateOfBirth: new Date('1990-01-01'),
      }
    });

    caseSheet = await prisma.caseSheet.create({
      data: {
        tenantId: tenant.id,
        patientId: patient.id,
        clinicalNotes: 'Tooth pain',
      }
    });

    tooth = await prisma.tooth.create({
      data: {
        tenantId: tenant.id,
        caseSheetId: caseSheet.id,
        toothNumber: 3,
        quadrant: 1,
        positionInQuadrant: 3,
        toothName: 'Upper Right Canine',
      }
    });

    setCurrentUserId(dentist.id);
  });

  describe('Simplified Finding Model', () => {
    it('should create findings with simplified structure', async () => {
      const finding = await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth.id,
          description: 'Large carious lesion on occlusal surface with pulpal involvement',
          recordedById: dentist.id,
          recordedDate: new Date(),
        }
      });

      expect(finding.toothId).toBe(tooth.id);
      expect(finding.description).toBe('Large carious lesion on occlusal surface with pulpal involvement');
      expect(finding.recordedById).toBe(dentist.id);
      expect(finding.recordedDate).toBeInstanceOf(Date);
      expect(finding.createdById).toBe(dentist.id);
      expect(finding.updatedById).toBe(dentist.id);

      // Verify no complex categorization fields exist
      expect(finding).not.toHaveProperty('category');
      expect(finding).not.toHaveProperty('subcategory');
      expect(finding).not.toHaveProperty('severity');
      expect(finding).not.toHaveProperty('prognosis');
    });

    it('should support free text descriptions as per PRD', async () => {
      const findings = await Promise.all([
        prisma.finding.create({
          data: {
            
            tenantId: tenant.id,toothId: tooth.id,
            description: 'Mesial caries extending to dentin',
            recordedById: dentist.id,
          }
        }),
        prisma.finding.create({
          data: {
            
            tenantId: tenant.id,toothId: tooth.id,
            description: 'Fractured cusp on buccal surface',
            recordedById: dentist.id,
          }
        }),
        prisma.finding.create({
          data: {
            
            tenantId: tenant.id,toothId: tooth.id,
            description: 'Gingival inflammation and bleeding on probing',
            recordedById: dentist.id,
          }
        }),
      ]);

      expect(findings).toHaveLength(3);
      findings.forEach(finding => {
        expect(finding.description).toBeTruthy();
        expect(typeof finding.description).toBe('string');
        expect(finding.toothId).toBe(tooth.id);
      });
    });

    it('should maintain direct relationship to treatments', async () => {
      const finding = await prisma.finding.create({
        data: {
          
          tenantId: tenant.id,toothId: tooth.id,
          description: 'Deep carious lesion',
          recordedById: dentist.id,
        }
      });

      // Create treatments directly from finding (no diagnosis layer)
      const treatment1 = await prisma.treatment.create({
        data: {
          findingId: finding.id,
          procedureName: 'Composite Restoration',
          cost: 200.00,
          status: 'PENDING',
        }
      });

      const treatment2 = await prisma.treatment.create({
        data: {
          findingId: finding.id,
          procedureName: 'Pulp Cap',
          cost: 100.00,
          status: 'PENDING',
        }
      });

      // Verify direct relationship
      const findingWithTreatments = await prisma.finding.findUnique({
        where: { id: finding.id },
        include: { treatments: true }
      });

      expect(findingWithTreatments!.treatments).toHaveLength(2);
      expect(findingWithTreatments!.treatments[0].findingId).toBe(finding.id);
      expect(findingWithTreatments!.treatments[1].findingId).toBe(finding.id);
    });

    it('should handle multiple findings per tooth', async () => {
      const findings = await Promise.all([
        prisma.finding.create({
          data: {
            
            tenantId: tenant.id,toothId: tooth.id,
            description: 'Occlusal caries',
            recordedById: dentist.id,
          }
        }),
        prisma.finding.create({
          data: {
            
            tenantId: tenant.id,toothId: tooth.id,
            description: 'Mesial caries',
            recordedById: dentist.id,
          }
        }),
        prisma.finding.create({
          data: {
            
            tenantId: tenant.id,toothId: tooth.id,
            description: 'Gingival recession',
            recordedById: dentist.id,
          }
        }),
      ]);

      const toothWithFindings = await prisma.tooth.findUnique({
        where: { id: tooth.id },
        include: { findings: true }
      });

      expect(toothWithFindings!.findings).toHaveLength(3);
      expect(toothWithFindings!.findings.every(f => f.toothId === tooth.id)).toBe(true);
    });
  });

  describe('Simplified Treatment Model', () => {
    let finding: any;

    beforeEach(async () => {
      finding = await prisma.finding.create({
        data: {
          
          tenantId: tenant.id,toothId: tooth.id,
          description: 'Test finding for treatment',
          recordedById: dentist.id,
        }
      });
    });

    it('should create treatments with simplified status', async () => {
      const treatment = await prisma.treatment.create({
        data: {
          findingId: finding.id,
          procedureName: 'Composite Restoration - Class II',
          cost: 275.50,
          status: 'PENDING',
        }
      });

      expect(treatment.findingId).toBe(finding.id);
      expect(treatment.procedureName).toBe('Composite Restoration - Class II');
      expect(treatment.cost.toNumber()).toBe(275.50);
      expect(treatment.status).toBe('PENDING');
      expect(treatment.createdById).toBe(dentist.id);

      // Verify no complex workflow fields exist
      expect(treatment).not.toHaveProperty('priority');
      expect(treatment).not.toHaveProperty('plannedDate');
      expect(treatment).not.toHaveProperty('assignedToId');
    });

    it('should handle simplified status transitions', async () => {
      const treatment = await prisma.treatment.create({
        data: {
          findingId: finding.id,
          procedureName: 'Crown Preparation',
          cost: 800.00,
          status: 'PENDING',
        }
      });

      expect(treatment.status).toBe('PENDING');
      expect(treatment.completedDate).toBeNull();
      expect(treatment.completedById).toBeNull();

      // Complete the treatment
      const completedTreatment = await prisma.treatment.update({
        where: { id: treatment.id },
        data: {
          status: 'COMPLETED',
          completedDate: new Date(),
          completedById: dentist.id,
        }
      });

      expect(completedTreatment.status).toBe('COMPLETED');
      expect(completedTreatment.completedDate).toBeInstanceOf(Date);
      expect(completedTreatment.completedById).toBe(dentist.id);
    });

    it('should support various procedure types and costs', async () => {
      const treatments = await Promise.all([
        prisma.treatment.create({
          data: {
            findingId: finding.id,
            procedureName: 'Amalgam Restoration',
            cost: 150.00,
            status: 'PENDING',
          }
        }),
        prisma.treatment.create({
          data: {
            findingId: finding.id,
            procedureName: 'Root Canal Therapy',
            cost: 1200.00,
            status: 'PENDING',
          }
        }),
        prisma.treatment.create({
          data: {
            findingId: finding.id,
            procedureName: 'Prophylaxis',
            cost: 80.00,
            status: 'COMPLETED',
            completedDate: new Date(),
            completedById: dentist.id,
          }
        }),
      ]);

      expect(treatments).toHaveLength(3);
      
      // Verify cost handling
      expect(treatments[0].cost.toNumber()).toBe(150.00);
      expect(treatments[1].cost.toNumber()).toBe(1200.00);
      expect(treatments[2].cost.toNumber()).toBe(80.00);

      // Verify status handling
      expect(treatments[0].status).toBe('PENDING');
      expect(treatments[1].status).toBe('PENDING');
      expect(treatments[2].status).toBe('COMPLETED');
      expect(treatments[2].completedDate).toBeInstanceOf(Date);
    });

    it('should maintain relationship integrity with findings', async () => {
      // Create multiple findings
      const finding2 = await prisma.finding.create({
        data: {
          
          tenantId: tenant.id,toothId: tooth.id,
          description: 'Second finding',
          recordedById: dentist.id,
        }
      });

      // Create treatments for different findings
      const treatment1 = await prisma.treatment.create({
        data: {
          findingId: finding.id,
          procedureName: 'Treatment for finding 1',
          cost: 100.00,
        }
      });

      const treatment2 = await prisma.treatment.create({
        data: {
          findingId: finding2.id,
          procedureName: 'Treatment for finding 2',
          cost: 200.00,
        }
      });

      // Verify relationships
      const findingWithTreatments = await prisma.finding.findUnique({
        where: { id: finding.id },
        include: { treatments: true }
      });

      const finding2WithTreatments = await prisma.finding.findUnique({
        where: { id: finding2.id },
        include: { treatments: true }
      });

      expect(findingWithTreatments!.treatments).toHaveLength(1);
      expect(findingWithTreatments!.treatments[0].id).toBe(treatment1.id);

      expect(finding2WithTreatments!.treatments).toHaveLength(1);
      expect(finding2WithTreatments!.treatments[0].id).toBe(treatment2.id);
    });

    it('should handle treatment completion workflow', async () => {
      const treatment = await prisma.treatment.create({
        data: {
          findingId: finding.id,
          procedureName: 'Extraction',
          cost: 300.00,
          status: 'PENDING',
        }
      });

      // Verify initial state
      expect(treatment.status).toBe('PENDING');
      expect(treatment.completedDate).toBeNull();
      expect(treatment.completedById).toBeNull();

      // Complete treatment
      const now = new Date();
      const completedTreatment = await prisma.treatment.update({
        where: { id: treatment.id },
        data: {
          status: 'COMPLETED',
          completedDate: now,
          completedById: dentist.id,
        }
      });

      expect(completedTreatment.status).toBe('COMPLETED');
      expect(completedTreatment.completedDate).toEqual(now);
      expect(completedTreatment.completedById).toBe(dentist.id);
      expect(completedTreatment.updatedById).toBe(dentist.id);
      expect(completedTreatment.updatedAt.getTime()).toBeGreaterThan(treatment.updatedAt.getTime());
    });
  });

  describe('Simplified Appointment Model', () => {
    it('should create appointments with simplified types', async () => {
      const consultation = await prisma.appointment.create({
        data: {
          
          tenantId: tenant.id,patientId: patient.id,
          appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          durationMinutes: 60,
          appointmentType: 'CONSULTATION',
          status: 'SCHEDULED',
          primaryProviderId: dentist.id,
          notes: 'Initial consultation appointment',
        }
      });

      const treatment = await prisma.appointment.create({
        data: {
          
          tenantId: tenant.id,patientId: patient.id,
          appointmentDate: new Date(Date.now() + 48 * 60 * 60 * 1000),
          durationMinutes: 90,
          appointmentType: 'TREATMENT',
          status: 'SCHEDULED',
          primaryProviderId: dentist.id,
        }
      });

      const checkup = await prisma.appointment.create({
        data: {
          
          tenantId: tenant.id,patientId: patient.id,
          appointmentDate: new Date(Date.now() + 72 * 60 * 60 * 1000),
          durationMinutes: 30,
          appointmentType: 'CHECKUP',
          status: 'SCHEDULED',
        }
      });

      expect(consultation.appointmentType).toBe('CONSULTATION');
      expect(treatment.appointmentType).toBe('TREATMENT');
      expect(checkup.appointmentType).toBe('CHECKUP');

      // Verify no complex features exist
      expect(consultation).not.toHaveProperty('recallType');
      expect(consultation).not.toHaveProperty('specialEquipmentNeeded');
      expect(consultation).not.toHaveProperty('wheelchairAccessible');
      expect(consultation).not.toHaveProperty('interpreterNeeded');
      expect(consultation).not.toHaveProperty('patientSatisfactionScore');
    });

    it('should handle simplified appointment statuses', async () => {
      const appointment = await prisma.appointment.create({
        data: {
          
          tenantId: tenant.id,patientId: patient.id,
          appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          appointmentType: 'CONSULTATION',
          status: 'SCHEDULED',
        }
      });

      expect(appointment.status).toBe('SCHEDULED');

      // Complete appointment
      const completedAppointment = await prisma.appointment.update({
        where: { id: appointment.id },
        data: { status: 'COMPLETED' }
      });

      expect(completedAppointment.status).toBe('COMPLETED');

      // Cancel appointment
      const cancelledAppointment = await prisma.appointment.update({
        where: { id: appointment.id },
        data: { status: 'CANCELLED' }
      });

      expect(cancelledAppointment.status).toBe('CANCELLED');
    });

    it('should maintain essential scheduling functionality', async () => {
      const appointmentDate = new Date(Date.now() + 24 * 60 * 60 * 1000);
      
      const appointment = await prisma.appointment.create({
        data: {
          
          tenantId: tenant.id,patientId: patient.id,
          appointmentDate,
          durationMinutes: 45,
          appointmentType: 'TREATMENT',
          status: 'SCHEDULED',
          primaryProviderId: dentist.id,
          notes: 'Follow-up treatment',
        }
      });

      expect(appointment.appointmentDate).toEqual(appointmentDate);
      expect(appointment.durationMinutes).toBe(45);
      expect(appointment.primaryProviderId).toBe(dentist.id);
      expect(appointment.notes).toBe('Follow-up treatment');
      expect(appointment.patientId).toBe(patient.id);
    });
  });

  describe('Simplified Payment and Invoice Models', () => {
    it('should create payments with simplified methods', async () => {
      const invoice = await prisma.invoice.create({
        data: {
          
          tenantId: tenant.id,patientId: patient.id,
          invoiceNumber: `INV-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 500.00,
          balanceDue: 500.00,
        }
      });

      const payments = await Promise.all([
        prisma.payment.create({
          data: {
            
            tenantId: tenant.id,patientId: patient.id,
            invoiceId: invoice.id,
            amount: 100.00,
            paymentDate: new Date(),
            paymentMethod: 'CASH',
            status: 'COMPLETED',
          }
        }),
        prisma.payment.create({
          data: {
            
            tenantId: tenant.id,patientId: patient.id,
            invoiceId: invoice.id,
            amount: 150.00,
            paymentDate: new Date(),
            paymentMethod: 'CARD',
            status: 'COMPLETED',
          }
        }),
        prisma.payment.create({
          data: {
            
            tenantId: tenant.id,patientId: patient.id,
            invoiceId: invoice.id,
            amount: 100.00,
            paymentDate: new Date(),
            paymentMethod: 'CHECK',
            status: 'PENDING',
          }
        }),
        prisma.payment.create({
          data: {
            
            tenantId: tenant.id,patientId: patient.id,
            invoiceId: invoice.id,
            amount: 100.00,
            paymentDate: new Date(),
            paymentMethod: 'BANK_TRANSFER',
            status: 'COMPLETED',
          }
        }),
        prisma.payment.create({
          data: {
            
            tenantId: tenant.id,patientId: patient.id,
            invoiceId: invoice.id,
            amount: 50.00,
            paymentDate: new Date(),
            paymentMethod: 'OTHER',
            status: 'COMPLETED',
            notes: 'Insurance payment',
          }
        }),
      ]);

      expect(payments).toHaveLength(5);
      expect(payments[0].paymentMethod).toBe('CASH');
      expect(payments[1].paymentMethod).toBe('CARD');
      expect(payments[2].paymentMethod).toBe('CHECK');
      expect(payments[3].paymentMethod).toBe('BANK_TRANSFER');
      expect(payments[4].paymentMethod).toBe('OTHER');

      // Verify no complex processing fields exist
      payments.forEach(payment => {
        expect(payment).not.toHaveProperty('transactionId');
        expect(payment).not.toHaveProperty('processorResponse');
        expect(payment).not.toHaveProperty('cardLastFour');
        expect(payment).not.toHaveProperty('processingFee');
        expect(payment).not.toHaveProperty('isDeposited');
      });
    });

    it('should create invoices with simplified statuses', async () => {
      const invoices = await Promise.all([
        prisma.invoice.create({
          data: {
            
            tenantId: tenant.id,patientId: patient.id,
            invoiceNumber: `DRAFT-${Date.now()}`,
            invoiceDate: new Date(),
            totalAmount: 200.00,
            balanceDue: 200.00,
            status: 'DRAFT',
          }
        }),
        prisma.invoice.create({
          data: {
            
            tenantId: tenant.id,patientId: patient.id,
            invoiceNumber: `SENT-${Date.now()}`,
            invoiceDate: new Date(),
            totalAmount: 300.00,
            balanceDue: 300.00,
            status: 'SENT',
          }
        }),
        prisma.invoice.create({
          data: {
            
            tenantId: tenant.id,patientId: patient.id,
            invoiceNumber: `PAID-${Date.now()}`,
            invoiceDate: new Date(),
            totalAmount: 400.00,
            amountPaid: 400.00,
            balanceDue: 0.00,
            status: 'PAID',
          }
        }),
        prisma.invoice.create({
          data: {
            
            tenantId: tenant.id,patientId: patient.id,
            invoiceNumber: `OVERDUE-${Date.now()}`,
            invoiceDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            totalAmount: 500.00,
            balanceDue: 500.00,
            status: 'OVERDUE',
          }
        }),
      ]);

      expect(invoices[0].status).toBe('DRAFT');
      expect(invoices[1].status).toBe('SENT');
      expect(invoices[2].status).toBe('PAID');
      expect(invoices[3].status).toBe('OVERDUE');

      // Verify no advanced features exist
      invoices.forEach(invoice => {
        expect(invoice).not.toHaveProperty('textToPaySent');
        expect(invoice).not.toHaveProperty('textToPayLink');
        expect(invoice).not.toHaveProperty('paymentTermsDays');
      });
    });
  });

  describe('Model Relationships and Data Integrity', () => {
    it('should maintain referential integrity across simplified models', async () => {
      // Create complete workflow
      const finding = await prisma.finding.create({
        data: {
          
          tenantId: tenant.id,toothId: tooth.id,
          description: 'Complex carious lesion',
          recordedById: dentist.id,
        }
      });

      const treatment = await prisma.treatment.create({
        data: {
          findingId: finding.id,
          procedureName: 'Crown and Bridge',
          cost: 1500.00,
          status: 'COMPLETED',
          completedDate: new Date(),
          completedById: dentist.id,
        }
      });

      const invoice = await prisma.invoice.create({
        data: {
          
          tenantId: tenant.id,patientId: patient.id,
          invoiceNumber: `INTEGRITY-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 1500.00,
          balanceDue: 1500.00,
          status: 'SENT',
        }
      });

      const payment = await prisma.payment.create({
        data: {
          
          tenantId: tenant.id,patientId: patient.id,
          invoiceId: invoice.id,
          amount: 1500.00,
          paymentDate: new Date(),
          paymentMethod: 'CARD',
          status: 'COMPLETED',
        }
      });

      // Verify complete relationship chain
      const completeData = await prisma.patient.findUnique({
        where: { id: patient.id },
        include: {
          caseSheet: {
            include: {
              teeth: {
                include: {
                  findings: {
                    include: {
                      treatments: true
                    }
                  }
                }
              }
            }
          },
          invoices: {
            include: {
              payments: true
            }
          }
        }
      });

      expect(completeData).toBeTruthy();
      expect(completeData!.caseSheets[0].teeth[0].findings[0].treatments[0].cost).toBe(1500.00);
      expect(completeData!.invoices[0].payments[0].amount).toBe(1500.00);
    });

    it('should handle cascade operations correctly', async () => {
      // Create finding with treatments
      const finding = await prisma.finding.create({
        data: {
          
          tenantId: tenant.id,toothId: tooth.id,
          description: 'Test finding for cascade',
          recordedById: dentist.id,
        }
      });

      const treatments = await Promise.all([
        prisma.treatment.create({
          data: {
            findingId: finding.id,
            procedureName: 'Treatment 1',
            cost: 100.00,
          }
        }),
        prisma.treatment.create({
          data: {
            findingId: finding.id,
            procedureName: 'Treatment 2',
            cost: 200.00,
          }
        }),
      ]);

      // Verify treatments exist
      const findingWithTreatments = await prisma.finding.findUnique({
        where: { id: finding.id },
        include: { treatments: true }
      });

      expect(findingWithTreatments!.treatments).toHaveLength(2);

      // Delete finding should handle treatments appropriately
      // (This depends on your cascade configuration in the schema)
      const treatmentIds = treatments.map(t => t.id);
      
      // Verify treatments still exist before deletion
      const treatmentsBefore = await prisma.treatment.findMany({
        where: { id: { in: treatmentIds } }
      });
      expect(treatmentsBefore).toHaveLength(2);
    });
  });
});