import { describe, it, expect, beforeEach } from 'vitest';
import { prisma } from '@/lib/prisma';
import { TenantContextManager } from '@/lib/tenant-context';
import { setCurrentUserId, getCurrentUserId, AuditUtils } from '@/lib/prisma-extensions/audit-fields';

describe('Comprehensive Audit Trail Tests', () => {
  let tenant: any;
  let adminUser: any;
  let dentistUser: any;
  let staffUser: any;

  beforeEach(async () => {
    // Create test tenant
    tenant = await prisma.tenant.create({
      data: {
        id: `test-tenant-${Date.now()}`,
        name: `Test Tenant ${Date.now()}`,
  
      },
    });
    
    TenantContextManager.setGlobalContext({ tenantId: tenant.id });

    // Create users for audit testing
    adminUser = await prisma.user.createAdminUser({
      tenantId: tenant.id,
      username: 'admin1',
      password: 'password123',
      firstName: 'Admin',
      lastName: 'User',
    });

    dentistUser = await prisma.user.createDentistUser({
      tenantId: tenant.id,
      username: 'dentist1',
      password: 'password123',
      firstName: 'Dr. <PERSON>',
      lastName: 'Smith',
    });

    staffUser = await prisma.user.createStaffUser({
      tenantId: tenant.id,
      username: 'staff1',
      password: 'password123',
      firstName: 'Staff',
      lastName: 'Member',
    });
  });

  describe('Audit Field Population', () => {
    it('should automatically populate audit fields on create operations', async () => {
      setCurrentUserId(adminUser.id);

      const patient = await prisma.patient.create({
        data: {
          tenantId: tenant.id,
          firstName: 'Test',
          lastName: 'Patient',
          phoneNumber: '+**********',
          dateOfBirth: new Date('1990-01-01'),
        },
      });

      expect(patient.createdById).toBe(adminUser.id);
      expect(patient.updatedById).toBe(adminUser.id);
      expect(patient.createdAt).toBeInstanceOf(Date);
      expect(patient.updatedAt).toBeInstanceOf(Date);
      expect(patient.createdAt.getTime()).toBeLessThanOrEqual(patient.updatedAt.getTime());
    });

    it('should automatically populate audit fields on update operations', async () => {
      setCurrentUserId(adminUser.id);

      const patient = await prisma.patient.create({
        data: {
          tenantId: tenant.id,
          firstName: 'Test',
          lastName: 'Patient',
          phoneNumber: '+**********',
        },
      });

      const originalCreatedAt = patient.createdAt;
      const originalCreatedById = patient.createdById;

      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));

      // Update with different user
      setCurrentUserId(dentistUser.id);

      const updatedPatient = await prisma.patient.update({
        where: { id: patient.id },
        data: { firstName: 'Updated' },
      });

      expect(updatedPatient.createdById).toBe(originalCreatedById); // Should remain unchanged
      expect(updatedPatient.updatedById).toBe(dentistUser.id); // Should be updated
      expect(updatedPatient.createdAt).toEqual(originalCreatedAt); // Should remain unchanged
      expect(updatedPatient.updatedAt.getTime()).toBeGreaterThan(patient.updatedAt.getTime());
    });

    it('should handle audit fields in createMany operations', async () => {
      setCurrentUserId(staffUser.id);

      const result = await prisma.patient.createMany({
        data: [
          {
            tenantId: tenant.id,
            firstName: 'Patient1',
            lastName: 'Test',
            phoneNumber: '+**********',
          },
          {
            tenantId: tenant.id,
            firstName: 'Patient2',
            lastName: 'Test',
            phoneNumber: '+**********',
          },
        ],
      });

      expect(result.count).toBe(2);

      const patients = await prisma.patient.findMany({
        where: { lastName: 'Test' },
        orderBy: { firstName: 'asc' },
      });

      expect(patients).toHaveLength(2);
      patients.forEach(patient => {
        expect(patient.createdById).toBe(staffUser.id);
        expect(patient.updatedById).toBe(staffUser.id);
        expect(patient.createdAt).toBeInstanceOf(Date);
        expect(patient.updatedAt).toBeInstanceOf(Date);
      });
    });

    it('should handle audit fields in upsert operations', async () => {
      setCurrentUserId(adminUser.id);

      // Test upsert create path
      const patient1 = await prisma.patient.upsert({
        where: { id: 999999 }, // Non-existent ID
        create: {
          tenantId: tenant.id,
          firstName: 'New',
          lastName: 'Patient',
          phoneNumber: '+**********',
        },
        update: {
          firstName: 'Updated',
        },
      });

      expect(patient1.createdById).toBe(adminUser.id);
      expect(patient1.updatedById).toBe(adminUser.id);
      expect(patient1.firstName).toBe('New');

      // Test upsert update path
      setCurrentUserId(dentistUser.id);

      const patient2 = await prisma.patient.upsert({
        where: { id: patient1.id },
        create: {
          tenantId: tenant.id,
          firstName: 'New2',
          lastName: 'Patient2',
          phoneNumber: '+**********',
        },
        update: {
          firstName: 'Updated',
        },
      });

      expect(patient2.id).toBe(patient1.id);
      expect(patient2.createdById).toBe(adminUser.id); // Should remain unchanged
      expect(patient2.updatedById).toBe(dentistUser.id); // Should be updated
      expect(patient2.firstName).toBe('Updated');
    });
  });

  describe('Audit Trail Across Complete Workflows', () => {
    it('should maintain audit trails throughout clinical workflow', async () => {
      // Admin creates patient
      setCurrentUserId(adminUser.id);
      const patient = await prisma.patient.create({
        data: {
          tenantId: tenant.id,
          firstName: 'Clinical',
          lastName: 'Patient',
          phoneNumber: '+**********',
        },
      });

      // Staff creates case sheet
      setCurrentUserId(staffUser.id);
      const caseSheet = await prisma.caseSheet.create({
        data: {
          tenantId: tenant.id,
          patientId: patient.id,
          clinicalNotes: 'Tooth pain',
        },
      });

      // Staff creates tooth
      const tooth = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 11,
          quadrant: 1,
          positionInQuadrant: 1,
          toothName: 'Upper Right Central Incisor',
        },
      });

      // Dentist records finding
      setCurrentUserId(dentistUser.id);
      const finding = await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth.id,
          description: 'Large carious lesion',
          recordedById: dentistUser.id,
        },
      });

      // Dentist creates treatment
      const treatment = await prisma.treatment.create({
        data: {
          tenantId: tenant.id,
          findingId: finding.id,
          procedureName: 'Composite Restoration',
          cost: 250.00,
        },
      });

      // Staff creates invoice
      setCurrentUserId(staffUser.id);
      const invoice = await prisma.invoice.create({
        data: {
          tenantId: tenant.id,
          patientId: patient.id,
          invoiceNumber: `INV-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 250.00,
          balanceDue: 250.00,
        },
      });

      // Staff records payment
      const payment = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patient.id,
          invoiceId: invoice.id,
          amount: 250.00,
          paymentDate: new Date(),
          paymentMethod: 'CASH',
        },
      });

      // Verify audit trails
      expect(patient.createdById).toBe(adminUser.id);
      expect(caseSheet.createdById).toBe(staffUser.id);
      expect(tooth.createdById).toBe(staffUser.id);
      expect(finding.createdById).toBe(dentistUser.id);
      expect(treatment.createdById).toBe(dentistUser.id);
      expect(invoice.createdById).toBe(staffUser.id);
      expect(payment.createdById).toBe(staffUser.id);

      // All should have proper timestamps
      const records = [patient, caseSheet, tooth, finding, treatment, invoice, payment];
      records.forEach(record => {
        expect(record.createdAt).toBeInstanceOf(Date);
        expect(record.updatedAt).toBeInstanceOf(Date);
        expect(record.updatedById).toBeTruthy();
      });
    });

    it('should track changes through multiple updates', async () => {
      setCurrentUserId(adminUser.id);

      const patient = await prisma.patient.create({
        data: {
          tenantId: tenant.id,
          firstName: 'Original',
          lastName: 'Patient',
          phoneNumber: '+**********',
        },
      });

      const originalCreatedAt = patient.createdAt;
      const originalCreatedById = patient.createdById;

      // First update by staff
      setCurrentUserId(staffUser.id);
      const update1 = await prisma.patient.update({
        where: { id: patient.id },
        data: { firstName: 'Updated1' },
      });

      // Second update by dentist
      setCurrentUserId(dentistUser.id);
      const update2 = await prisma.patient.update({
        where: { id: patient.id },
        data: { firstName: 'Updated2' },
      });

      // Third update by admin
      setCurrentUserId(adminUser.id);
      const update3 = await prisma.patient.update({
        where: { id: patient.id },
        data: { firstName: 'Final' },
      });

      // Verify audit trail integrity
      expect(update3.createdAt).toEqual(originalCreatedAt); // Never changes
      expect(update3.createdById).toBe(originalCreatedById); // Never changes
      expect(update3.updatedById).toBe(adminUser.id); // Latest updater
      expect(update3.updatedAt.getTime()).toBeGreaterThan(update2.updatedAt.getTime());
      expect(update2.updatedAt.getTime()).toBeGreaterThan(update1.updatedAt.getTime());
    });
  });

  describe('Audit Utilities', () => {
    it('should create audit fields manually', () => {
      setCurrentUserId(adminUser.id);

      const auditFields = AuditUtils.createAuditFields();

      expect(auditFields.createdById).toBe(adminUser.id);
      expect(auditFields.updatedById).toBe(adminUser.id);
      expect(auditFields.createdAt).toBeInstanceOf(Date);
      expect(auditFields.updatedAt).toBeInstanceOf(Date);
    });

    it('should create audit fields with explicit user ID', () => {
      const auditFields = AuditUtils.createAuditFields(dentistUser.id);

      expect(auditFields.createdById).toBe(dentistUser.id);
      expect(auditFields.updatedById).toBe(dentistUser.id);
      expect(auditFields.createdAt).toBeInstanceOf(Date);
      expect(auditFields.updatedAt).toBeInstanceOf(Date);
    });

    it('should create update audit fields', () => {
      setCurrentUserId(staffUser.id);

      const updateFields = AuditUtils.updateAuditFields();

      expect(updateFields.updatedById).toBe(staffUser.id);
      expect(updateFields.updatedAt).toBeInstanceOf(Date);
      expect(updateFields).not.toHaveProperty('createdById');
      expect(updateFields).not.toHaveProperty('createdAt');
    });

    it('should validate audit fields', () => {
      // Valid audit fields
      const validFields = {
        createdById: adminUser.id,
        updatedById: dentistUser.id,
        createdAt: new Date(Date.now() - 1000),
        updatedAt: new Date(),
      };

      const validResult = AuditUtils.validateAuditFields(validFields);
      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      // Invalid audit fields - createdAt after updatedAt
      const invalidFields = {
        createdById: adminUser.id,
        updatedById: dentistUser.id,
        createdAt: new Date(),
        updatedAt: new Date(Date.now() - 1000),
      };

      const invalidResult = AuditUtils.validateAuditFields(invalidFields);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('createdAt cannot be after updatedAt');
    });

    it('should get audit trail information', () => {
      const record = {
        createdById: adminUser.id,
        updatedById: dentistUser.id,
        createdAt: new Date(Date.now() - 1000),
        updatedAt: new Date(),
      };

      const auditTrail = AuditUtils.getAuditTrail(record);

      expect(auditTrail.created.by).toBe(adminUser.id);
      expect(auditTrail.created.at).toEqual(record.createdAt);
      expect(auditTrail.updated.by).toBe(dentistUser.id);
      expect(auditTrail.updated.at).toEqual(record.updatedAt);
    });
  });

  describe('Current User ID Management', () => {
    it('should manage current user ID correctly', () => {
      expect(getCurrentUserId()).toBeNull();

      setCurrentUserId(adminUser.id);
      expect(getCurrentUserId()).toBe(adminUser.id);

      setCurrentUserId(dentistUser.id);
      expect(getCurrentUserId()).toBe(dentistUser.id);

      setCurrentUserId(null);
      expect(getCurrentUserId()).toBeNull();
    });

    it('should handle operations without current user ID', async () => {
      setCurrentUserId(null);

      const patient = await prisma.patient.create({
        data: {
          tenantId: tenant.id,
          firstName: 'No User',
          lastName: 'Patient',
          phoneNumber: '+**********',
        },
      });

      expect(patient.createdById).toBeNull();
      expect(patient.updatedById).toBeNull();
      expect(patient.createdAt).toBeInstanceOf(Date);
      expect(patient.updatedAt).toBeInstanceOf(Date);
    });
  });

  describe('Compliance Requirements', () => {
    it('should preserve audit fields across all models with audit support', async () => {
      setCurrentUserId(adminUser.id);

      // Create a complete workflow with audit fields
      const patient = await prisma.patient.create({
        data: {
          tenantId: tenant.id,
          firstName: 'Compliance',
          lastName: 'Patient',
          phoneNumber: '+**********',
        },
      });

      const caseSheet = await prisma.caseSheet.create({
        data: {
          tenantId: tenant.id,
          patientId: patient.id,
          clinicalNotes: 'Compliance test',
        },
      });

      const tooth = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 11,
          quadrant: 1,
          positionInQuadrant: 1,
          toothName: 'Upper Right Central Incisor',
        },
      });

      const finding = await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth.id,
          description: 'Compliance finding',
          recordedById: adminUser.id,
        },
      });

      const treatment = await prisma.treatment.create({
        data: {
          tenantId: tenant.id,
          findingId: finding.id,
          procedureName: 'Compliance treatment',
          cost: 100.00,
        },
      });

      const appointment = await prisma.appointment.create({
        data: {
          tenantId: tenant.id,
          patientId: patient.id,
          appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          appointmentType: 'CONSULTATION',
        },
      });

      const invoice = await prisma.invoice.create({
        data: {
          tenantId: tenant.id,
          patientId: patient.id,
          invoiceNumber: `COMP-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 100.00,
          balanceDue: 100.00,
        },
      });

      const payment = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patient.id,
          invoiceId: invoice.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'CASH',
        },
      });

      // Verify all records have audit fields
      const records = [patient, caseSheet, tooth, finding, treatment, appointment, invoice, payment];
      
      records.forEach(record => {
        expect(record.createdById).toBe(adminUser.id);
        expect(record.updatedById).toBe(adminUser.id);
        expect(record.createdAt).toBeInstanceOf(Date);
        expect(record.updatedAt).toBeInstanceOf(Date);
      });
    });

    it('should maintain audit trail integrity over time', async () => {
      setCurrentUserId(adminUser.id);

      // Create the required data structure
      const patientRecord = await prisma.patient.create({
        data: {
          tenantId: tenant.id,
          firstName: 'Test',
          lastName: 'Patient',
          phoneNumber: '+**********',
          dateOfBirth: new Date('1990-01-01'),
        },
      });

      const caseSheet = await prisma.caseSheet.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          clinicalNotes: 'Audit trail test',
        }
      });

      const tooth = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 11,
          quadrant: 1,
          positionInQuadrant: 1,
          toothName: 'Upper Right Central Incisor',
        }
      });

      const finding = await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth.id,
          description: 'Test finding for audit',
          recordedById: dentistUser.id,
          recordedDate: new Date(),
        }
      });

      const treatment = await prisma.treatment.create({
        data: {
          tenantId: tenant.id,
          findingId: finding.id,
          procedureName: 'Long-term treatment',
          cost: 500.00,
          status: 'PENDING',
        },
      });

      const auditHistory = [];

      // Simulate multiple updates over time
      for (let i = 0; i < 3; i++) {
        await new Promise(resolve => setTimeout(resolve, 10));
        
        const userId = [adminUser.id, dentistUser.id, staffUser.id][i];
        setCurrentUserId(userId);

        const updated = await prisma.treatment.update({
          where: { id: treatment.id },
          data: { cost: 500.00 + (i * 100) },
        });

        auditHistory.push({
          updatedById: updated.updatedById,
          updatedAt: updated.updatedAt,
          cost: updated.cost,
        });
      }

      // Verify audit history
      expect(auditHistory).toHaveLength(3);
      expect(auditHistory[0].updatedById).toBe(adminUser.id);
      expect(auditHistory[1].updatedById).toBe(dentistUser.id);
      expect(auditHistory[2].updatedById).toBe(staffUser.id);

      // Verify timestamps are in order
      expect(auditHistory[1].updatedAt.getTime()).toBeGreaterThan(auditHistory[0].updatedAt.getTime());
      expect(auditHistory[2].updatedAt.getTime()).toBeGreaterThan(auditHistory[1].updatedAt.getTime());
    });
  });
});