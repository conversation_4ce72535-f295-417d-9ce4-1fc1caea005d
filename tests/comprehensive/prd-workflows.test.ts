import { describe, it, expect, beforeEach } from 'vitest';
import { prisma } from '@/lib/prisma';
import { TenantContextManager } from '@/lib/tenant-context';
import { setCurrentUserId } from '@/lib/prisma-extensions/audit-fields';

describe('PRD-Specified User Flows Tests', () => {
  let tenant: any;
  let dentist: any;
  let staff: any;
  let patient: any;
  let patientRecord: any;

  beforeEach(async () => {
    // Create test tenant
    tenant = await prisma.tenant.create({
      data: {
        id: `test-tenant-${Date.now()}`,
        name: `Test Tenant ${Date.now()}`,
      },
    });
    
    TenantContextManager.setGlobalContext({ tenantId: tenant.id });

    // Create users
    dentist = await prisma.user.createDentistUser({
      tenantId: tenant.id,
      username: 'dentist1',
      password: 'password123',
      firstName: 'Dr. <PERSON>',
      lastName: '<PERSON>',
    });

    staff = await prisma.user.createStaffUser({
      tenantId: tenant.id,
      username: 'staff1',
      password: 'password123',
      firstName: 'Jane',
      lastName: 'Doe',
    });

    patient = await prisma.user.createPatientUser({
      tenantId: tenant.id,
      phoneNumber: '+**********',
      password: 'password123',
      firstName: 'Alice',
      lastName: 'Johnson',
    });

    // Create patient record
    patientRecord = await prisma.patient.create({
      data: {
        tenantId: tenant.id,
        userId: patient.id,
        firstName: 'Alice',
        lastName: 'Johnson',
        phoneNumber: '+**********',
        dateOfBirth: new Date('1990-01-01'),
        email: '<EMAIL>',
      },
    });

    setCurrentUserId(dentist.id);
  });

  describe('Core PRD Workflow: Patient → Case Sheet → Findings → Treatments → Invoicing', () => {
    it('should execute the complete PRD-specified clinical and billing workflow', async () => {
      // Step 1: Create appointment (entry point as per PRD)
      setCurrentUserId(staff.id);
      const appointment = await prisma.appointment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          durationMinutes: 60,
          appointmentType: 'CONSULTATION',
          status: 'SCHEDULED',
          primaryProviderId: dentist.id,
          notes: 'Initial consultation as per PRD',
        },
      });

      expect(appointment.patientId).toBe(patientRecord.id);
      expect(appointment.appointmentType).toBe('CONSULTATION');
      expect(appointment.status).toBe('SCHEDULED');

      // Step 2: Create case sheet (single case sheet per patient as per PRD)
      const caseSheet = await prisma.caseSheet.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          clinicalNotes: 'Tooth pain in upper right quadrant',
          historyOfPresentIllness: 'Pain started 3 days ago',
          pastMedicalHistory: 'No significant medical history',
          pastDentalHistory: 'Regular cleanings, no previous major work',
        },
      });

      expect(caseSheet.patientId).toBe(patientRecord.id);
      expect(caseSheet.clinicalNotes).toBe('Tooth pain in upper right quadrant');

      // Step 3: Create teeth for examination (FDI notation as per PRD)
      const tooth1 = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 13, // FDI notation
          quadrant: 1,
          position: 3,
          isPresent: true,
        },
      });

      const tooth2 = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 14, // FDI notation
          quadrant: 1,
          position: 4,
          isPresent: true,
        },
      });

      expect(tooth1.toothNumber).toBe(13);
      expect(tooth2.toothNumber).toBe(14);

      // Step 4: Record findings (simplified, free text as per PRD)
      setCurrentUserId(dentist.id);
      const finding1 = await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth1.id,
          description: 'Large carious lesion on occlusal surface extending to dentin',
          recordedById: dentist.id,
          recordedDate: new Date(),
        },
      });

      const finding2 = await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth2.id,
          description: 'Small carious lesion on mesial surface, enamel only',
          recordedById: dentist.id,
          recordedDate: new Date(),
        },
      });

      expect(finding1.description).toBe('Large carious lesion on occlusal surface extending to dentin');
      expect(finding1.recordedById).toBe(dentist.id);
      expect(finding2.toothId).toBe(tooth2.id);

      // Step 5: Create treatments directly from findings (no diagnosis layer as per PRD)
      const treatment1 = await prisma.treatment.create({
        data: {
          tenantId: tenant.id,
          findingId: finding1.id,
          procedureName: 'Composite Restoration - Large (Class II)',
          cost: 275.00,
          status: 'PENDING',
        },
      });

      const treatment2 = await prisma.treatment.create({
        data: {
          tenantId: tenant.id,
          findingId: finding2.id,
          procedureName: 'Composite Restoration - Small (Class III)',
          cost: 185.00,
          status: 'PENDING',
        },
      });

      expect(treatment1.findingId).toBe(finding1.id);
      expect(treatment1.cost).toBe(275.00);
      expect(treatment1.status).toBe('PENDING');

      // Step 6: Complete first treatment (simplified status as per PRD)
      const completedTreatment1 = await prisma.treatment.update({
        where: { id: treatment1.id },
        data: {
          status: 'COMPLETED',
          completedDate: new Date(),
          completedById: dentist.id,
        },
      });

      expect(completedTreatment1.status).toBe('COMPLETED');
      expect(completedTreatment1.completedById).toBe(dentist.id);
      expect(completedTreatment1.completedDate).toBeInstanceOf(Date);

      // Step 7: Create invoice for completed treatments (simplified status as per PRD)
      setCurrentUserId(staff.id);
      const invoice = await prisma.invoice.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceNumber: `INV${new Date().toISOString().slice(0, 10).replace(/-/g, '')}001`,
          invoiceDate: new Date(),
          totalAmount: 275.00, // Only completed treatment
          amountPaid: 0.00,
          balanceDue: 275.00,
          status: 'DRAFT',
        },
      });

      expect(invoice.patientId).toBe(patientRecord.id);
      expect(invoice.totalAmount).toBe(275.00);
      expect(invoice.balanceDue).toBe(275.00);
      expect(invoice.status).toBe('DRAFT');

      // Step 8: Send invoice (simplified status transition as per PRD)
      const sentInvoice = await prisma.invoice.update({
        where: { id: invoice.id },
        data: { status: 'SENT' },
      });

      expect(sentInvoice.status).toBe('SENT');

      // Step 9: Record partial payment (simplified payment methods as per PRD)
      const payment1 = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 150.00,
          paymentDate: new Date(),
          paymentMethod: 'CASH',
          status: 'COMPLETED',
          notes: 'Partial payment - cash',
        },
      });

      expect(payment1.amount).toBe(150.00);
      expect(payment1.paymentMethod).toBe('CASH');
      expect(payment1.status).toBe('COMPLETED');

      // Step 10: Update invoice with payment
      const updatedInvoice1 = await prisma.invoice.update({
        where: { id: invoice.id },
        data: {
          amountPaid: 150.00,
          balanceDue: 125.00,
        },
      });

      expect(updatedInvoice1.amountPaid).toBe(150.00);
      expect(updatedInvoice1.balanceDue).toBe(125.00);

      // Step 11: Record final payment (different payment method as per PRD)
      const payment2 = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 125.00,
          paymentDate: new Date(),
          paymentMethod: 'CARD',
          status: 'COMPLETED',
          notes: 'Final payment - credit card',
        },
      });

      // Step 12: Mark invoice as paid (final status as per PRD)
      const paidInvoice = await prisma.invoice.update({
        where: { id: invoice.id },
        data: {
          amountPaid: 275.00,
          balanceDue: 0.00,
          status: 'PAID',
        },
      });

      expect(paidInvoice.status).toBe('PAID');
      expect(paidInvoice.balanceDue).toBe(0.00);

      // Step 13: Complete second treatment and create second invoice
      setCurrentUserId(dentist.id);
      const completedTreatment2 = await prisma.treatment.update({
        where: { id: treatment2.id },
        data: {
          status: 'COMPLETED',
          completedDate: new Date(),
          completedById: dentist.id,
        },
      });

      setCurrentUserId(staff.id);
      const invoice2 = await prisma.invoice.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceNumber: `INV${new Date().toISOString().slice(0, 10).replace(/-/g, '')}002`,
          invoiceDate: new Date(),
          totalAmount: 185.00,
          amountPaid: 0.00,
          balanceDue: 185.00,
          status: 'SENT',
        },
      });

      // Step 14: Verify complete PRD workflow data integrity
      const workflowData = await prisma.patient.findUnique({
        where: { id: patientRecord.id },
        include: {
          caseSheet: {
            include: {
              teeth: {
                include: {
                  findings: {
                    include: {
                      treatments: true,
                    },
                  },
                },
              },
            },
          },
          appointments: true,
          invoices: {
            include: {
              payments: true,
            },
          },
          payments: true,
        },
      });

      // Verify patient data
      expect(workflowData).toBeTruthy();
      expect(workflowData!.firstName).toBe('Alice');

      // Verify case sheet (single per patient as per PRD)
      expect(workflowData!.caseSheet).toBeTruthy();
      expect(workflowData!.caseSheet!.clinicalNotes).toBe('Tooth pain in upper right quadrant');

      // Verify teeth (FDI notation)
      expect(workflowData!.caseSheet!.teeth).toHaveLength(2);
      expect(workflowData!.caseSheet!.teeth[0].toothNumber).toBe(13);
      expect(workflowData!.caseSheet!.teeth[1].toothNumber).toBe(14);

      // Verify findings (free text descriptions)
      const allFindings = workflowData!.caseSheet!.teeth.flatMap(t => t.findings);
      expect(allFindings).toHaveLength(2);
      expect(allFindings[0].description).toContain('Large carious lesion');
      expect(allFindings[1].description).toContain('Small carious lesion');

      // Verify treatments (direct from findings, simplified status)
      const allTreatments = allFindings.flatMap(f => f.treatments);
      expect(allTreatments).toHaveLength(2);
      expect(allTreatments.every(t => t.status === 'COMPLETED')).toBe(true);

      // Verify appointments (simplified types)
      expect(workflowData!.appointments).toHaveLength(1);
      expect(workflowData!.appointments[0].appointmentType).toBe('CONSULTATION');

      // Verify invoices (simplified status)
      expect(workflowData!.invoices).toHaveLength(2);
      expect(workflowData!.invoices.find(i => i.status === 'PAID')).toBeTruthy();
      expect(workflowData!.invoices.find(i => i.status === 'SENT')).toBeTruthy();

      // Verify payments (simplified methods)
      expect(workflowData!.payments).toHaveLength(2);
      expect(workflowData!.payments.find(p => p.paymentMethod === 'CASH')).toBeTruthy();
      expect(workflowData!.payments.find(p => p.paymentMethod === 'CARD')).toBeTruthy();
      expect(workflowData!.payments.every(p => p.status === 'COMPLETED')).toBe(true);

      // Verify total amounts match PRD requirements
      const totalTreatmentCost = allTreatments.reduce((sum, t) => sum + Number(t.cost), 0);
      const totalInvoiceAmount = workflowData!.invoices.reduce((sum, i) => sum + Number(i.totalAmount), 0);
      const totalPaymentAmount = workflowData!.payments.reduce((sum, p) => sum + Number(p.amount), 0);

      expect(totalTreatmentCost).toBe(460.00);
      expect(totalInvoiceAmount).toBe(460.00);
      expect(totalPaymentAmount).toBe(275.00); // Only first invoice paid
    });
  });

  describe('Simplified Appointment Types and Statuses (PRD Requirement)', () => {
    it('should handle all simplified appointment types as per PRD', async () => {
      setCurrentUserId(staff.id);

      // Test all 3 simplified appointment types (reduced from 12)
      const consultation = await prisma.appointment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          appointmentType: 'CONSULTATION',
          status: 'SCHEDULED',
        },
      });

      const treatment = await prisma.appointment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          appointmentDate: new Date(Date.now() + 48 * 60 * 60 * 1000),
          appointmentType: 'TREATMENT',
          status: 'SCHEDULED',
        },
      });

      const checkup = await prisma.appointment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          appointmentDate: new Date(Date.now() + 72 * 60 * 60 * 1000),
          appointmentType: 'CHECKUP',
          status: 'SCHEDULED',
        },
      });

      expect(consultation.appointmentType).toBe('CONSULTATION');
      expect(treatment.appointmentType).toBe('TREATMENT');
      expect(checkup.appointmentType).toBe('CHECKUP');

      // Test all 3 simplified statuses (reduced from complex workflow)
      const completedAppointment = await prisma.appointment.update({
        where: { id: consultation.id },
        data: { status: 'COMPLETED' },
      });

      const cancelledAppointment = await prisma.appointment.update({
        where: { id: treatment.id },
        data: { status: 'CANCELLED' },
      });

      expect(completedAppointment.status).toBe('COMPLETED');
      expect(cancelledAppointment.status).toBe('CANCELLED');
      expect(checkup.status).toBe('SCHEDULED');
    });
  });

  describe('Simplified Payment Methods and Statuses (PRD Requirement)', () => {
    let invoice: any;

    beforeEach(async () => {
      setCurrentUserId(staff.id);
      invoice = await prisma.invoice.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceNumber: `INV-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 500.00,
          balanceDue: 500.00,
          status: 'SENT',
        },
      });
    });

    it('should handle all simplified payment methods as per PRD', async () => {
      // Test all 5 simplified payment methods (reduced from 17)
      const cashPayment = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'CASH',
          status: 'COMPLETED',
        },
      });

      const cardPayment = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 150.00,
          paymentDate: new Date(),
          paymentMethod: 'CARD',
          status: 'COMPLETED',
        },
      });

      const checkPayment = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'CHECK',
          status: 'PENDING',
        },
      });

      const bankTransferPayment = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'BANK_TRANSFER',
          status: 'COMPLETED',
        },
      });

      const otherPayment = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 50.00,
          paymentDate: new Date(),
          paymentMethod: 'OTHER',
          status: 'COMPLETED',
          notes: 'Insurance payment',
        },
      });

      expect(cashPayment.paymentMethod).toBe('CASH');
      expect(cardPayment.paymentMethod).toBe('CARD');
      expect(checkPayment.paymentMethod).toBe('CHECK');
      expect(bankTransferPayment.paymentMethod).toBe('BANK_TRANSFER');
      expect(otherPayment.paymentMethod).toBe('OTHER');

      // Test simplified payment statuses (3 statuses)
      expect(checkPayment.status).toBe('PENDING');
      
      const processedCheck = await prisma.payment.update({
        where: { id: checkPayment.id },
        data: { status: 'COMPLETED' },
      });

      expect(processedCheck.status).toBe('COMPLETED');

      // Test failed payment status
      const failedPayment = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          amount: 25.00,
          paymentDate: new Date(),
          paymentMethod: 'CARD',
          status: 'FAILED',
          notes: 'Card declined',
        },
      });

      expect(failedPayment.status).toBe('FAILED');
    });
  });

  describe('Simplified Invoice Statuses (PRD Requirement)', () => {
    it('should handle all simplified invoice statuses as per PRD', async () => {
      setCurrentUserId(staff.id);

      // Test all 4 simplified invoice statuses (reduced from 11)
      const draftInvoice = await prisma.invoice.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceNumber: `DRAFT-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 200.00,
          amountPaid: 0.00,
          balanceDue: 200.00,
          status: 'DRAFT',
        },
      });

      expect(draftInvoice.status).toBe('DRAFT');

      // Send invoice
      const sentInvoice = await prisma.invoice.update({
        where: { id: draftInvoice.id },
        data: { status: 'SENT' },
      });

      expect(sentInvoice.status).toBe('SENT');

      // Mark as paid
      const paidInvoice = await prisma.invoice.update({
        where: { id: draftInvoice.id },
        data: {
          status: 'PAID',
          amountPaid: 200.00,
          balanceDue: 0.00,
        },
      });

      expect(paidInvoice.status).toBe('PAID');
      expect(paidInvoice.balanceDue).toBe(0.00);

      // Test overdue invoice
      const overdueInvoice = await prisma.invoice.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceNumber: `OVERDUE-${Date.now()}`,
          invoiceDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
          totalAmount: 300.00,
          amountPaid: 0.00,
          balanceDue: 300.00,
          status: 'OVERDUE',
        },
      });

      expect(overdueInvoice.status).toBe('OVERDUE');
    });
  });

  describe('Simplified Treatment Status (PRD Requirement)', () => {
    let finding: any;

    beforeEach(async () => {
      setCurrentUserId(staff.id);
      const caseSheet = await prisma.caseSheet.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          clinicalNotes: 'Test complaint',
        },
      });

      const tooth = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 11,
          quadrant: 1,
          position: 1,
        },
      });

      setCurrentUserId(dentist.id);
      finding = await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth.id,
          description: 'Test finding for treatment',
          recordedById: dentist.id,
        },
      });
    });

    it('should handle simplified treatment statuses as per PRD', async () => {
      // Test 2 simplified treatment statuses (reduced from complex workflow)
      const pendingTreatment = await prisma.treatment.create({
        data: {
          tenantId: tenant.id,
          findingId: finding.id,
          procedureName: 'Root Canal Therapy',
          cost: 800.00,
          status: 'PENDING',
        },
      });

      expect(pendingTreatment.status).toBe('PENDING');
      expect(pendingTreatment.completedDate).toBeNull();
      expect(pendingTreatment.completedById).toBeNull();

      // Complete the treatment
      const completedTreatment = await prisma.treatment.update({
        where: { id: pendingTreatment.id },
        data: {
          status: 'COMPLETED',
          completedDate: new Date(),
          completedById: dentist.id,
        },
      });

      expect(completedTreatment.status).toBe('COMPLETED');
      expect(completedTreatment.completedDate).toBeInstanceOf(Date);
      expect(completedTreatment.completedById).toBe(dentist.id);
    });
  });

  describe('Simplified User Types (PRD Requirement)', () => {
    it('should handle all simplified user types as per PRD', async () => {
      // Test all 4 simplified user types (reduced from complex role system)
      const admin = await prisma.user.create({
        data: {
          tenantId: tenant.id,
          username: 'admin1',
          password: '$2a$12$hashedpassword',
          firstName: 'Admin',
          lastName: 'User',
          userType: 'ADMIN',
        },
      });

      const dentistUser = await prisma.user.create({
        data: {
          tenantId: tenant.id,
          username: 'dentist2',
          password: '$2a$12$hashedpassword',
          firstName: 'Dr. Jane',
          lastName: 'Doe',
          userType: 'DENTIST',
        },
      });

      const staffUser = await prisma.user.create({
        data: {
          tenantId: tenant.id,
          username: 'staff2',
          password: '$2a$12$hashedpassword',
          firstName: 'Staff',
          lastName: 'Member',
          userType: 'STAFF',
        },
      });

      const patientUser = await prisma.user.create({
        data: {
          tenantId: tenant.id,
          phoneNumber: '+**********',
          username: '+**********',
          password: '$2a$12$hashedpassword',
          firstName: 'Patient',
          lastName: 'User',
          userType: 'PATIENT',
        },
      });

      expect(admin.userType).toBe('ADMIN');
      expect(dentistUser.userType).toBe('DENTIST');
      expect(staffUser.userType).toBe('STAFF');
      expect(patientUser.userType).toBe('PATIENT');

      // Verify computed properties work correctly
      expect(admin.isClinicalProvider).toBe(true);
      expect(dentistUser.isClinicalProvider).toBe(true);
      expect(staffUser.isClinicalProvider).toBe(false);
      expect(patientUser.isClinicalProvider).toBe(false);
    });
  });

  describe('Direct Finding-to-Treatment Workflow (PRD Requirement)', () => {
    it('should support direct finding-to-treatment workflow without diagnosis layer', async () => {
      setCurrentUserId(staff.id);
      const caseSheet = await prisma.caseSheet.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          clinicalNotes: 'Direct workflow test',
        },
      });

      const tooth = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 21,
          quadrant: 2,
          position: 1,
        },
      });

      setCurrentUserId(dentist.id);
      const finding = await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth.id,
          description: 'Deep carious lesion requiring immediate treatment',
          recordedById: dentist.id,
        },
      });

      // Create multiple treatments directly from the finding (no diagnosis step)
      const treatment1 = await prisma.treatment.create({
        data: {
          tenantId: tenant.id,
          findingId: finding.id,
          procedureName: 'Pulpotomy',
          cost: 300.00,
          status: 'PENDING',
        },
      });

      const treatment2 = await prisma.treatment.create({
        data: {
          tenantId: tenant.id,
          findingId: finding.id,
          procedureName: 'Crown Placement',
          cost: 1200.00,
          status: 'PENDING',
        },
      });

      // Verify direct relationship
      const findingWithTreatments = await prisma.finding.findUnique({
        where: { id: finding.id },
        include: { treatments: true },
      });

      expect(findingWithTreatments!.treatments).toHaveLength(2);
      expect(findingWithTreatments!.treatments[0].findingId).toBe(finding.id);
      expect(findingWithTreatments!.treatments[1].findingId).toBe(finding.id);
      expect(findingWithTreatments!.treatments[0].procedureName).toBe('Pulpotomy');
      expect(findingWithTreatments!.treatments[1].procedureName).toBe('Crown Placement');

      // Verify no diagnosis model exists in the relationship chain
      expect(findingWithTreatments!.treatments[0]).not.toHaveProperty('diagnosisId');
      expect(findingWithTreatments!.treatments[1]).not.toHaveProperty('diagnosisId');
    });
  });

  describe('Single Case Sheet Per Patient (PRD Requirement)', () => {
    it('should enforce single case sheet per patient as per PRD', async () => {
      setCurrentUserId(staff.id);

      // Create first case sheet
      const caseSheet1 = await prisma.caseSheet.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          clinicalNotes: 'First case sheet',
        },
      });

      expect(caseSheet1.patientId).toBe(patientRecord.id);

      // Attempting to create second case sheet should fail due to unique constraint
      await expect(
        prisma.caseSheet.create({
          data: {
            tenantId: tenant.id,
            patientId: patientRecord.id,
            clinicalNotes: 'Second case sheet',
          },
        })
      ).rejects.toThrow();

      // Verify only one case sheet exists
      const caseSheets = await prisma.caseSheet.findMany({
        where: { patientId: patientRecord.id },
      });

      expect(caseSheets).toHaveLength(1);
      expect(caseSheets[0].clinicalNotes).toBe('First case sheet');
    });
  });
});