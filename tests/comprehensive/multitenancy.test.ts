import { describe, it, expect, beforeEach } from 'vitest';
import { prisma } from '@/lib/prisma';
import { TenantContextManager } from '@/lib/tenant-context';
import { setCurrentUserId } from '@/lib/prisma-extensions/audit-fields';

describe('Comprehensive Multitenancy Tests', () => {
  let tenant1: any;
  let tenant2: any;

  beforeEach(async () => {
    // Create test tenants directly
    tenant1 = await prisma.tenant.create({
      data: {
        id: `tenant-1-${Date.now()}`,
        name: `Tenant 1 ${Date.now()}`,
      },
    });

    tenant2 = await prisma.tenant.create({
      data: {
        id: `tenant-2-${Date.now()}`,
        name: `Tenant 2 ${Date.now()}`,
      },
    });
  });

  describe('Tenant Isolation Across All Models', () => {
    it('should maintain tenant isolation for users', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const user1 = await prisma.user.createPatientUser({
        tenantId: tenant1.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'User',
        lastName: 'One',
      });

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });

      const user2 = await prisma.user.createPatientUser({
        tenantId: tenant2.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'User',
        lastName: 'Two',
      });

      // Switch back to tenant1 and verify isolation
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });
      const tenant1Users = await prisma.user.findMany();
      expect(tenant1Users).toHaveLength(1);
      expect(tenant1Users[0].id).toBe(user1.id);

      // Switch to tenant2 and verify isolation
      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      const tenant2Users = await prisma.user.findMany();
      expect(tenant2Users).toHaveLength(1);
      expect(tenant2Users[0].id).toBe(user2.id);
    });

    it('should maintain tenant isolation for patients', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const patient1 = await prisma.patient.create({
        data: {
          tenantId: tenant1.id,
          firstName: 'Patient',
          lastName: 'One',
          phoneNumber: '+**********',
        },
      });

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });

      const patient2 = await prisma.patient.create({
        data: {
          tenantId: tenant2.id,
          firstName: 'Patient',
          lastName: 'Two',
          phoneNumber: '+**********',
        },
      });

      // Verify isolation
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });
      const tenant1Patients = await prisma.patient.findMany();
      expect(tenant1Patients).toHaveLength(1);
      expect(tenant1Patients[0].id).toBe(patient1.id);

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      const tenant2Patients = await prisma.patient.findMany();
      expect(tenant2Patients).toHaveLength(1);
      expect(tenant2Patients[0].id).toBe(patient2.id);
    });

    it('should maintain tenant isolation for appointments', async () => {
      // Create patients in different tenants
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });
      const patient1 = await prisma.patient.create({
        data: {
          tenantId: tenant1.id,
          firstName: 'Patient',
          lastName: 'One',
        },
      });

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      const patient2 = await prisma.patient.create({
        data: {
          tenantId: tenant2.id,
          firstName: 'Patient',
          lastName: 'Two',
        },
      });

      // Create appointments
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });
      const appointment1 = await prisma.appointment.create({
        data: {
          tenantId: tenant1.id,
          patientId: patient1.id,
          appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          appointmentType: 'CONSULTATION',
        },
      });

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      const appointment2 = await prisma.appointment.create({
        data: {
          tenantId: tenant2.id,
          patientId: patient2.id,
          appointmentDate: new Date(Date.now() + 48 * 60 * 60 * 1000),
          appointmentType: 'TREATMENT',
        },
      });

      // Verify isolation
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });
      const tenant1Appointments = await prisma.appointment.findMany();
      expect(tenant1Appointments).toHaveLength(1);
      expect(tenant1Appointments[0].id).toBe(appointment1.id);

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      const tenant2Appointments = await prisma.appointment.findMany();
      expect(tenant2Appointments).toHaveLength(1);
      expect(tenant2Appointments[0].id).toBe(appointment2.id);
    });

    it('should maintain tenant isolation for complete clinical workflow', async () => {
      // Set up complete workflow in tenant1
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });
      
      const dentist1 = await prisma.user.createDentistUser({
        tenantId: tenant1.id,
        username: 'dentist1',
        password: 'password123',
        firstName: 'Dr. One',
        lastName: 'Dentist',
      });

      const patient1 = await prisma.patient.create({
        data: {
          tenantId: tenant1.id,
          firstName: 'Patient',
          lastName: 'One',
        },
      });

      const caseSheet1 = await prisma.caseSheet.create({
        data: {
          tenantId: tenant1.id,
          patientId: patient1.id,
          clinicalNotes: 'Tenant 1 complaint',
        },
      });

      const tooth1 = await prisma.tooth.create({
        data: {
          tenantId: tenant1.id,
          caseSheetId: caseSheet1.id,
          toothNumber: 11,
          quadrant: 1,
          position: 1,
        },
      });

      const finding1 = await prisma.finding.create({
        data: {
          tenantId: tenant1.id,
          toothId: tooth1.id,
          description: 'Tenant 1 finding',
          recordedById: dentist1.id,
        },
      });

      const treatment1 = await prisma.treatment.create({
        data: {
          tenantId: tenant1.id,
          findingId: finding1.id,
          procedureName: 'Tenant 1 treatment',
          cost: 100.00,
        },
      });

      const invoice1 = await prisma.invoice.create({
        data: {
          tenantId: tenant1.id,
          patientId: patient1.id,
          invoiceNumber: `T1-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 100.00,
          balanceDue: 100.00,
        },
      });

      const payment1 = await prisma.payment.create({
        data: {
          tenantId: tenant1.id,
          patientId: patient1.id,
          invoiceId: invoice1.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'CASH',
        },
      });

      // Set up similar workflow in tenant2
      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      
      const dentist2 = await prisma.user.createDentistUser({
        tenantId: tenant2.id,
        username: 'dentist2',
        password: 'password123',
        firstName: 'Dr. Two',
        lastName: 'Dentist',
      });

      const patient2 = await prisma.patient.create({
        data: {
          tenantId: tenant2.id,
          firstName: 'Patient',
          lastName: 'Two',
        },
      });

      const caseSheet2 = await prisma.caseSheet.create({
        data: {
          tenantId: tenant2.id,
          patientId: patient2.id,
          clinicalNotes: 'Tenant 2 complaint',
        },
      });

      const tooth2 = await prisma.tooth.create({
        data: {
          tenantId: tenant2.id,
          caseSheetId: caseSheet2.id,
          toothNumber: 21,
          quadrant: 2,
          position: 1,
        },
      });

      const finding2 = await prisma.finding.create({
        data: {
          tenantId: tenant2.id,
          toothId: tooth2.id,
          description: 'Tenant 2 finding',
          recordedById: dentist2.id,
        },
      });

      const treatment2 = await prisma.treatment.create({
        data: {
          tenantId: tenant2.id,
          findingId: finding2.id,
          procedureName: 'Tenant 2 treatment',
          cost: 200.00,
        },
      });

      const invoice2 = await prisma.invoice.create({
        data: {
          tenantId: tenant2.id,
          patientId: patient2.id,
          invoiceNumber: `T2-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 200.00,
          balanceDue: 200.00,
        },
      });

      const payment2 = await prisma.payment.create({
        data: {
          tenantId: tenant2.id,
          patientId: patient2.id,
          invoiceId: invoice2.id,
          amount: 200.00,
          paymentDate: new Date(),
          paymentMethod: 'CARD',
        },
      });

      // Verify complete isolation for tenant1
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });
      
      const t1Users = await prisma.user.findMany();
      const t1Patients = await prisma.patient.findMany();
      const t1CaseSheets = await prisma.caseSheet.findMany();
      const t1Teeth = await prisma.tooth.findMany();
      const t1Findings = await prisma.finding.findMany();
      const t1Treatments = await prisma.treatment.findMany();
      const t1Invoices = await prisma.invoice.findMany();
      const t1Payments = await prisma.payment.findMany();

      expect(t1Users).toHaveLength(1);
      expect(t1Patients).toHaveLength(1);
      expect(t1CaseSheets).toHaveLength(1);
      expect(t1Teeth).toHaveLength(1);
      expect(t1Findings).toHaveLength(1);
      expect(t1Treatments).toHaveLength(1);
      expect(t1Invoices).toHaveLength(1);
      expect(t1Payments).toHaveLength(1);

      expect(t1Users[0].firstName).toBe('Dr. One');
      expect(t1CaseSheets[0].clinicalNotes).toBe('Tenant 1 complaint');
      expect(t1Treatments[0].cost).toBe(100.00);

      // Verify complete isolation for tenant2
      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      
      const t2Users = await prisma.user.findMany();
      const t2Patients = await prisma.patient.findMany();
      const t2CaseSheets = await prisma.caseSheet.findMany();
      const t2Teeth = await prisma.tooth.findMany();
      const t2Findings = await prisma.finding.findMany();
      const t2Treatments = await prisma.treatment.findMany();
      const t2Invoices = await prisma.invoice.findMany();
      const t2Payments = await prisma.payment.findMany();

      expect(t2Users).toHaveLength(1);
      expect(t2Patients).toHaveLength(1);
      expect(t2CaseSheets).toHaveLength(1);
      expect(t2Teeth).toHaveLength(1);
      expect(t2Findings).toHaveLength(1);
      expect(t2Treatments).toHaveLength(1);
      expect(t2Invoices).toHaveLength(1);
      expect(t2Payments).toHaveLength(1);

      expect(t2Users[0].firstName).toBe('Dr. Two');
      expect(t2CaseSheets[0].clinicalNotes).toBe('Tenant 2 complaint');
      expect(t2Treatments[0].cost).toBe(200.00);
    });
  });

  describe('Tenant Context Management', () => {
    it('should handle tenant context switching correctly', async () => {
      // Initially no context
      expect(prisma.getCurrentTenantId()).toBeNull();

      // Set tenant1 context
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });
      expect(prisma.getCurrentTenantId()).toBe(tenant1.id);

      // Switch to tenant2 context
      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      expect(prisma.getCurrentTenantId()).toBe(tenant2.id);

      // Clear context
      TenantContextManager.clearGlobalContext();
      expect(prisma.getCurrentTenantId()).toBeNull();
    });

    it('should handle bypass tenant functionality', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      // Create users in both tenants
      const user1 = await prisma.user.createPatientUser({
        tenantId: tenant1.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'User',
        lastName: 'One',
      });

      const user2 = await prisma.user.createPatientUser({
        tenantId: tenant2.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'User',
        lastName: 'Two',
      });

      // Normal query should only return tenant1 users
      const normalUsers = await prisma.user.findMany();
      expect(normalUsers).toHaveLength(1);
      expect(normalUsers[0].id).toBe(user1.id);

      // Bypass query should return all users
      const allUsers = await prisma.bypassTenant(async () => {
        return await prisma.user.findMany({
          orderBy: { firstName: 'asc' }
        });
      });

      expect(allUsers).toHaveLength(2);
      expect(allUsers.find(u => u.id === user1.id)).toBeTruthy();
      expect(allUsers.find(u => u.id === user2.id)).toBeTruthy();

      // Verify context is restored after bypass
      expect(prisma.getCurrentTenantId()).toBe(tenant1.id);
    });
  });

  describe('Cross-Tenant Operations Prevention', () => {
    it('should prevent cross-tenant updates', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const user1 = await prisma.user.createPatientUser({
        tenantId: tenant1.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'User',
        lastName: 'One',
      });

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });

      const user2 = await prisma.user.createPatientUser({
        tenantId: tenant2.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'User',
        lastName: 'Two',
      });

      // Switch to tenant1 and try to update user2 (should fail)
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      await expect(
        prisma.user.update({
          where: { id: user2.id },
          data: { firstName: 'Updated' }
        })
      ).rejects.toThrow();
    });

    it('should prevent cross-tenant deletes', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      const patient1 = await prisma.patient.create({
        data: {
          tenantId: tenant1.id,
          firstName: 'Patient',
          lastName: 'One',
        },
      });

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });

      const patient2 = await prisma.patient.create({
        data: {
          tenantId: tenant2.id,
          firstName: 'Patient',
          lastName: 'Two',
        },
      });

      // Switch to tenant1 and try to delete patient2 (should fail)
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      await expect(
        prisma.patient.delete({
          where: { id: patient2.id }
        })
      ).rejects.toThrow();
    });
  });

  describe('Extension Methods with Multitenancy', () => {
    it('should respect tenant isolation in extension methods', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      await prisma.user.createDentistUser({
        tenantId: tenant1.id,
        username: 'dentist1',
        password: 'password123',
        firstName: 'Dr. One',
        lastName: 'Dentist',
      });

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });

      await prisma.user.createDentistUser({
        tenantId: tenant2.id,
        username: 'dentist2',
        password: 'password123',
        firstName: 'Dr. Two',
        lastName: 'Dentist',
      });

      // Test getUsersByType with tenant isolation
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });
      const tenant1Dentists = await prisma.user.getUsersByType('DENTIST', tenant1.id);
      expect(tenant1Dentists).toHaveLength(1);
      expect(tenant1Dentists[0].firstName).toBe('Dr. One');

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });
      const tenant2Dentists = await prisma.user.getUsersByType('DENTIST', tenant2.id);
      expect(tenant2Dentists).toHaveLength(1);
      expect(tenant2Dentists[0].firstName).toBe('Dr. Two');
    });

    it('should respect tenant isolation in authentication', async () => {
      TenantContextManager.setGlobalContext({ tenantId: tenant1.id });

      await prisma.user.createStaffUser({
        tenantId: tenant1.id,
        username: 'staff1',
        password: 'password123',
        firstName: 'Staff',
        lastName: 'One',
      });

      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });

      await prisma.user.createStaffUser({
        tenantId: tenant2.id,
        username: 'staff1', // Same username, different tenant
        password: 'password123',
        firstName: 'Staff',
        lastName: 'Two',
      });

      // Authentication should be tenant-specific
      const auth1 = await prisma.user.authenticate('staff1', 'password123', tenant1.id);
      expect(auth1).toBeTruthy();
      expect(auth1!.firstName).toBe('Staff');
      expect(auth1!.lastName).toBe('One');

      const auth2 = await prisma.user.authenticate('staff1', 'password123', tenant2.id);
      expect(auth2).toBeTruthy();
      expect(auth2!.firstName).toBe('Staff');
      expect(auth2!.lastName).toBe('Two');

      // Cross-tenant authentication should fail
      const crossAuth = await prisma.user.authenticate('staff1', 'password123', 'wrong-tenant');
      expect(crossAuth).toBeNull();
    });
  });
});