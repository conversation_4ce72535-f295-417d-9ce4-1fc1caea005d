import { describe, it, expect, beforeEach } from 'vitest';
import { prisma } from '@/lib/prisma';
import { TenantContextManager } from '@/lib/tenant-context';
import { setCurrentUserId } from '@/lib/prisma-extensions/audit-fields';
import { TestDataFactory } from '../setup';

describe('Comprehensive Extension Methods Tests', () => {
  let tenant: any;
  let dentist: any;
  let patient: any;
  let patientRecord: any;

  beforeEach(async () => {
    // Create test tenant directly without using TestDataFactory to avoid circular dependency
    tenant = await prisma.tenant.create({
      data: {
        id: `test-tenant-${Date.now()}`,
        name: `Test Tenant ${Date.now()}`,
  
      },
    });
    
    TenantContextManager.setGlobalContext({ tenantId: tenant.id });

    // Create dentist user using extension method
    dentist = await prisma.user.createDentistUser({
      tenantId: tenant.id,
      username: 'dentist1',
      password: 'password123',
      firstName: '<PERSON>. <PERSON>',
      lastName: '<PERSON>',
    });

    // Create patient user using extension method
    const patientUser = await prisma.user.createPatientUser({
      tenantId: tenant.id,
      phoneNumber: '+**********',
      password: 'password123',
      firstName: 'Alice',
      lastName: 'Johnson',
    });

    // Create patient record
    patientRecord = await prisma.patient.create({
      data: {
        tenantId: tenant.id,
        userId: patientUser.id,
        firstName: 'Alice',
        lastName: 'Johnson',
        phoneNumber: '+**********',
        dateOfBirth: new Date('1990-01-01'),
      },
    });

    setCurrentUserId(dentist.id);
  });

  describe('User Extension Methods', () => {
    it('should test createDentistUser method', async () => {
      const newDentist = await prisma.user.createDentistUser({
        tenantId: tenant.id,
        username: 'dentist2',
        password: 'password123',
        firstName: 'Dr. Jane',
        lastName: 'Doe',
      });

      expect(newDentist.userType).toBe('DENTIST');
      expect(newDentist.username).toBe('dentist2');
      expect(newDentist.firstName).toBe('Dr. Jane');
      expect(newDentist.tenantId).toBe(tenant.id);
    });

    it('should test createPatientUser method', async () => {
      const newPatient = await prisma.user.createPatientUser({
        tenantId: tenant.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Bob',
        lastName: 'Wilson',
      });

      expect(newPatient.userType).toBe('PATIENT');
      expect(newPatient.phoneNumber).toBe('+**********');
      expect(newPatient.username).toBe('+**********');
      expect(newPatient.firstName).toBe('Bob');
    });

    it('should test getUsersByType method', async () => {
      const dentists = await prisma.user.getUsersByType('DENTIST', tenant.id);
      expect(dentists).toHaveLength(1);
      expect(dentists[0].userType).toBe('DENTIST');
      expect(dentists[0].firstName).toBe('Dr. John');
    });

    it('should test authenticate method', async () => {
      const authenticatedUser = await prisma.user.authenticate(
        'dentist1',
        'password123',
        tenant.id
      );

      expect(authenticatedUser).toBeTruthy();
      expect(authenticatedUser!.username).toBe('dentist1');
      expect(authenticatedUser!.userType).toBe('DENTIST');
    });

    it('should test computed properties', async () => {
      const users = await prisma.user.findMany({
        where: { tenantId: tenant.id },
      });

      const dentistUser = users.find(u => u.userType === 'DENTIST');
      expect(dentistUser!.displayName).toBe('Dr. John Smith (dentist1)');
      expect(dentistUser!.isClinicalProvider).toBe(true);
      expect(dentistUser!.userTypeDisplay).toBe('Dentist');
      expect(dentistUser!.primaryAuthField).toBe('dentist1');
    });
  });

  describe('Appointment Extension Methods', () => {
    it('should test createAppointment method', async () => {
      const appointment = await prisma.appointment.createAppointment({
        tenantId: tenant.id,
        patientId: patientRecord.id,
        appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        appointmentType: 'CONSULTATION',
        primaryProviderId: dentist.id,
        notes: 'Initial consultation',
      });

      expect(appointment.patientId).toBe(patientRecord.id);
      expect(appointment.appointmentType).toBe('CONSULTATION');
      expect(appointment.status).toBe('SCHEDULED');
      expect(appointment.durationMinutes).toBe(60);
    });

    it('should test getTodaysAppointments method', async () => {
      // Create appointment for today
      const today = new Date();
      await prisma.appointment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          appointmentDate: today,
          appointmentType: 'CONSULTATION',
          status: 'SCHEDULED',
        },
      });

      const todaysAppointments = await prisma.appointment.getTodaysAppointments(tenant.id);
      expect(todaysAppointments).toHaveLength(1);
      expect(todaysAppointments[0].patientId).toBe(patientRecord.id);
    });

    it('should test validateAppointment method', async () => {
      const validation = await prisma.appointment.validateAppointment({
        tenantId: tenant.id,
        patientId: patientRecord.id,
        appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        durationMinutes: 60,
      });

      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should test computed properties', async () => {
      const appointment = await prisma.appointment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          appointmentDate: new Date(),
          appointmentType: 'TREATMENT',
          status: 'SCHEDULED',
          durationMinutes: 90,
        },
      });

      expect(appointment.durationDisplay).toBe('1h 30m');
      expect(appointment.isToday).toBe(true);
      expect(appointment.statusColor).toBe('blue');
    });
  });

  describe('Finding Extension Methods', () => {
    let caseSheet: any;
    let tooth: any;

    beforeEach(async () => {
      caseSheet = await prisma.caseSheet.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          clinicalNotes: 'Tooth pain',
        },
      });

      tooth = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 11,
          quadrant: 1,
          position: 1,
        },
      });
    });

    it('should test createFinding method', async () => {
      const finding = await prisma.finding.createFinding({
        tenantId: tenant.id,
        toothId: tooth.id,
        description: 'Large carious lesion on occlusal surface',
        recordedById: dentist.id,
      });

      expect(finding.toothId).toBe(tooth.id);
      expect(finding.description).toBe('Large carious lesion on occlusal surface');
      expect(finding.recordedById).toBe(dentist.id);
      expect(finding.createdById).toBe(dentist.id);
    });

    it('should test getFindingsForPatient method', async () => {
      await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth.id,
          description: 'Test finding',
          recordedById: dentist.id,
        },
      });

      const findings = await prisma.finding.getFindingsForPatient(tenant.id, patientRecord.id);
      expect(findings).toHaveLength(1);
      expect(findings[0].description).toBe('Test finding');
    });

    it('should test computed properties', async () => {
      const finding = await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth.id,
          description: 'This is a very long finding description that should be truncated when displayed in the UI because it exceeds the normal display length',
          recordedById: dentist.id,
        },
      });

      expect(finding.displayDescription).toContain('...');
      expect(finding.displayDescription.length).toBeLessThanOrEqual(100);
      expect(finding.formattedRecordedDate).toBeTruthy();
    });
  });

  describe('Treatment Extension Methods', () => {
    let finding: any;

    beforeEach(async () => {
      const caseSheet = await prisma.caseSheet.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          clinicalNotes: 'Tooth pain',
        },
      });

      const tooth = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 11,
          quadrant: 1,
          position: 1,
        },
      });

      finding = await prisma.finding.create({
        data: {
          tenantId: tenant.id,
          toothId: tooth.id,
          description: 'Test finding for treatment',
          recordedById: dentist.id,
        },
      });
    });

    it('should test createTreatment method', async () => {
      const treatment = await prisma.treatment.createTreatment({
        tenantId: tenant.id,
        findingId: finding.id,
        procedureName: 'Composite Restoration',
        cost: 250.00,
      });

      expect(treatment.findingId).toBe(finding.id);
      expect(treatment.procedureName).toBe('Composite Restoration');
      expect(treatment.cost).toBe(250.00);
      expect(treatment.status).toBe('PENDING');
    });

    it('should test markCompleted method', async () => {
      const treatment = await prisma.treatment.create({
        data: {
          tenantId: tenant.id,
          findingId: finding.id,
          procedureName: 'Crown Preparation',
          cost: 800.00,
          status: 'PENDING',
        },
      });

      const completedTreatment = await prisma.treatment.markCompleted(treatment.id, dentist.id);

      expect(completedTreatment.status).toBe('COMPLETED');
      expect(completedTreatment.completedById).toBe(dentist.id);
      expect(completedTreatment.completedDate).toBeInstanceOf(Date);
    });

    it('should test getCommonProcedures method', async () => {
      const procedures = prisma.treatment.getCommonProcedures();
      expect(procedures).toBeInstanceOf(Array);
      expect(procedures.length).toBeGreaterThan(0);
      expect(procedures[0]).toHaveProperty('name');
      expect(procedures[0]).toHaveProperty('averageCost');
    });

    it('should test computed properties', async () => {
      const treatment = await prisma.treatment.create({
        data: {
          tenantId: tenant.id,
          findingId: finding.id,
          procedureName: 'Root Canal',
          cost: 1200.50,
          status: 'COMPLETED',
        },
      });

      expect(treatment.isCompleted).toBe(true);
      expect(treatment.isPending).toBe(false);
      expect(treatment.formattedCost).toBe('1200.50');
      expect(treatment.statusDisplay).toBe('Completed');
    });
  });

  describe('Invoice Extension Methods', () => {
    it('should test generateInvoiceNumber method', async () => {
      const invoiceNumber = await prisma.invoice.generateInvoiceNumber(tenant.id);
      expect(invoiceNumber).toMatch(/^INV\d{8}\d{3}$/);
    });

    it('should test createInvoice method', async () => {
      const invoice = await prisma.invoice.createInvoice({
        tenantId: tenant.id,
        patientId: patientRecord.id,
        invoiceDate: new Date(),
        totalAmount: 500.00,
      });

      expect(invoice.patientId).toBe(patientRecord.id);
      expect(invoice.totalAmount).toBe(500.00);
      expect(invoice.balanceDue).toBe(500.00);
      expect(invoice.status).toBe('DRAFT');
      expect(invoice.invoiceNumber).toMatch(/^INV\d{8}\d{3}$/);
    });

    it('should test updateAmountPaid method', async () => {
      const invoice = await prisma.invoice.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceNumber: `INV-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 300.00,
          balanceDue: 300.00,
          status: 'SENT',
        },
      });

      const updatedInvoice = await prisma.invoice.updateAmountPaid(
        invoice.id,
        300.00,
        dentist.id
      );

      expect(updatedInvoice.amountPaid).toBe(300.00);
      expect(updatedInvoice.balanceDue).toBe(0.00);
      expect(updatedInvoice.status).toBe('PAID');
    });

    it('should test computed properties', async () => {
      const invoice = await prisma.invoice.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceNumber: 'INV20250101001',
          invoiceDate: new Date(),
          totalAmount: 400.00,
          amountPaid: 200.00,
          balanceDue: 200.00,
          status: 'SENT',
        },
      });

      expect(invoice.isPaidInFull).toBe(false);
      expect(invoice.paymentPercentage).toBe(50);
      expect(invoice.statusColor).toBe('blue');
      expect(invoice.displayInvoiceNumber).toBe('#INV20250101001');
      expect(invoice.canBeEdited).toBe(false);
    });
  });

  describe('Payment Extension Methods', () => {
    let invoice: any;

    beforeEach(async () => {
      invoice = await prisma.invoice.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceNumber: `INV-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 500.00,
          balanceDue: 500.00,
          status: 'SENT',
        },
      });
    });

    it('should test createPayment method', async () => {
      const payment = await prisma.payment.createPayment({
        tenantId: tenant.id,
        patientId: patientRecord.id,
        invoiceId: invoice.id,
        amount: 250.00,
        paymentDate: new Date(),
        paymentMethod: 'CASH',
        notes: 'Partial payment',
      });

      expect(payment.patientId).toBe(patientRecord.id);
      expect(payment.invoiceId).toBe(invoice.id);
      expect(payment.amount).toBe(250.00);
      expect(payment.paymentMethod).toBe('CASH');
      expect(payment.status).toBe('COMPLETED');
    });

    it('should test getPaymentSummary method', async () => {
      await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'CASH',
          status: 'COMPLETED',
        },
      });

      await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 150.00,
          paymentDate: new Date(),
          paymentMethod: 'CARD',
          status: 'COMPLETED',
        },
      });

      const summary = await prisma.payment.getPaymentSummary(tenant.id);
      expect(summary.totalPayments).toBe(2);
      expect(summary.totalAmount).toBe(250.00);
      expect(summary.methodBreakdown.CASH.count).toBe(1);
      expect(summary.methodBreakdown.CARD.count).toBe(1);
    });

    it('should test computed properties', async () => {
      const payment = await prisma.payment.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 125.75,
          paymentDate: new Date(),
          paymentMethod: 'BANK_TRANSFER',
          status: 'COMPLETED',
        },
      });

      expect(payment.isSuccessful).toBe(true);
      expect(payment.isElectronic).toBe(true);
      expect(payment.paymentMethodDisplayName).toBe('Bank Transfer');
      expect(payment.statusColor).toBe('green');
      expect(payment.displayAmount).toBe('125.75');
    });
  });
});