import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import { TenantContextManager } from '@/lib/tenant-context';
import { setCurrentUserId } from '@/lib/prisma-extensions/audit-fields';

// Test database setup
const testDatabaseUrl = process.env.TEST_DATABASE_URL || 'file:./tests.db';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = testDatabaseUrl;

// Create a separate Prisma client for testing
const testPrisma = new PrismaClient({
  datasources: {
    db: {
      url: testDatabaseUrl,
    },
  },
});

// Global test setup
beforeAll(async () => {
  // Clean up any existing data in the test database
  try {
    // Use Prisma's built-in database push to ensure schema is up-to-date
    console.log('Setting up test database schema...');
    // The schema should already be created via db push
  } catch (error) {
    console.warn('Failed to setup test database:', error);
  }
});

// Clean up after all tests
afterAll(async () => {
  await testPrisma.$disconnect();
});

// Reset state before each test
beforeEach(async () => {
  // Clear tenant context
  TenantContextManager.clearGlobalContext();
  
  // Clear current user ID
  setCurrentUserId(null);
  
  // Clean up test data to ensure test isolation
  try {
    await testPrisma.payment.deleteMany();
    await testPrisma.invoice.deleteMany();
    await testPrisma.treatment.deleteMany();
    await testPrisma.finding.deleteMany();
    await testPrisma.tooth.deleteMany();
    await testPrisma.caseSheet.deleteMany();
    await testPrisma.appointment.deleteMany();
    await testPrisma.patient.deleteMany();
    await testPrisma.user.deleteMany();
    await testPrisma.tenant.deleteMany();
  } catch (error) {
    console.warn('Failed to clean test data:', error);
  }
});

// Clean up after each test
afterEach(async () => {
  // Clear tenant context
  TenantContextManager.clearGlobalContext();
  
  // Clear current user ID
  setCurrentUserId(null);
});

// Export test utilities
export { testPrisma };

// Test data factories
export const TestDataFactory = {
  // Create a test tenant
  async createTestTenant(overrides: Partial<any> = {}) {
    const timestamp = Date.now();
    return testPrisma.tenant.create({
      data: {
        name: `Test Tenant ${timestamp}`,
        ...overrides,
      },
    });
  },

  // Create a test user
  async createTestUser(tenantId: string, overrides: Partial<any> = {}) {
    const timestamp = Date.now();
    const randomSuffix = Math.floor(Math.random() * 10000);
    return testPrisma.user.create({
      data: {
        tenantId,
        firstName: 'Test',
        lastName: 'User',
        phoneNumber: `+1234567${randomSuffix.toString().padStart(4, '0')}`,
        email: `test${timestamp}${randomSuffix}@example.com`,
        username: `testuser${timestamp}${randomSuffix}`,
        password: '$2a$12$hashedpassword', // Pre-hashed test password
        userType: 'PATIENT',
        isActive: true,
        ...overrides,
      },
    });
  },

  // Create a test patient
  async createTestPatient(tenantId: string, userId?: number, overrides: Partial<any> = {}) {
    const user = userId ? { id: userId } : await this.createTestUser(tenantId, { userType: 'PATIENT' });
    const randomSuffix = Math.floor(Math.random() * 10000);

    return testPrisma.patient.create({
      data: {
        tenantId,
        userId: user.id,
        firstName: 'Test',
        lastName: 'Patient',
        phoneNumber: `+1234567${randomSuffix.toString().padStart(4, '0')}`,
        dateOfBirth: new Date('1990-01-01'),
        ...overrides,
      },
    });
  },

  // Create a test appointment
  async createTestAppointment(tenantId: string, patientId: number, overrides: Partial<any> = {}) {
    return testPrisma.appointment.create({
      data: {
        tenantId,
        patientId,
        appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        durationMinutes: 60,
        appointmentType: 'CONSULTATION',
        status: 'SCHEDULED',
        ...overrides,
      },
    });
  },

  // Create a test finding
  async createTestFinding(tenantId: string, toothId: number, overrides: Partial<any> = {}) {
    return testPrisma.finding.create({
      data: {
        tenantId,
        toothId,
        description: 'Test finding description',
        recordedDate: new Date(),
        ...overrides,
      },
    });
  },

  // Create a test treatment
  async createTestTreatment(tenantId: string, findingId: number, overrides: Partial<any> = {}) {
    return testPrisma.treatment.create({
      data: {
        tenantId,
        findingId,
        procedureName: 'Test Procedure',
        cost: 100.00,
        status: 'PENDING',
        ...overrides,
      },
    });
  },

  // Create a test invoice
  async createTestInvoice(tenantId: string, patientId: number, overrides: Partial<any> = {}) {
    return testPrisma.invoice.create({
      data: {
        tenantId,
        patientId,
        invoiceNumber: `INV-${Date.now()}`,
        invoiceDate: new Date(),
        totalAmount: 100.00,
        amountPaid: 0.00,
        balanceDue: 100.00,
        status: 'DRAFT',
        ...overrides,
      },
    });
  },

  // Create a test payment
  async createTestPayment(tenantId: string, patientId: number, invoiceId?: number, overrides: Partial<any> = {}) {
    return testPrisma.payment.create({
      data: {
        tenantId,
        patientId,
        invoiceId,
        amount: 50.00,
        paymentDate: new Date(),
        paymentMethod: 'CASH',
        status: 'COMPLETED',
        ...overrides,
      },
    });
  },
};