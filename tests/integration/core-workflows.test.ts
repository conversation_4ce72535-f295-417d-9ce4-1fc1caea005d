import { describe, it, expect, beforeEach } from 'vitest';
import { prisma } from '@/lib/prisma';
import { TenantContextManager } from '@/lib/tenant-context';
import { setCurrentUserId } from '@/lib/prisma-extensions/audit-fields';
import { TestDataFactory } from '../setup';

describe('Core PRD Workflows Integration', () => {
  let tenant: any;
  let dentist: any;
  let staff: any;
  let patient: any;
  let patientRecord: any;

  beforeEach(async () => {
    // Set up test environment
    tenant = await TestDataFactory.createTestTenant();
    TenantContextManager.setGlobalContext({ tenantId: tenant.id });

    // Create users
    dentist = await prisma.user.createDentistUser({
      tenantId: tenant.id,
      username: 'dentist1',
      password: 'password123',
      firstName: 'Dr. <PERSON>',
      lastName: 'Smith',
    });

    staff = await prisma.user.createStaffUser({
      tenantId: tenant.id,
      username: 'staff1',
      password: 'password123',
      firstName: '<PERSON>',
      lastName: 'Doe',
    });

    patient = await prisma.user.createPatientUser({
      tenantId: tenant.id,
      phoneNumber: '+**********',
      password: 'password123',
      firstName: 'Alice',
      lastName: 'Johnson',
    });

    // Create patient record
    patientRecord = await prisma.patient.create({
      data: {
        tenantId: tenant.id,
        userId: patient.id,
        firstName: 'Alice',
        lastName: 'Johnson',
        phoneNumber: '+**********',
        dateOfBirth: new Date('1990-01-01'),
        email: '<EMAIL>',
      }
    });

    setCurrentUserId(dentist.id);
  });

  describe('Complete Patient → Case Sheet → Findings → Treatments → Invoicing Workflow', () => {
    it('should execute the complete clinical and billing workflow', async () => {
      // Step 1: Create appointment (entry point)
      const appointment = await prisma.appointment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          durationMinutes: 60,
          appointmentType: 'CONSULTATION',
          status: 'SCHEDULED',
          primaryProviderId: dentist.id,
          notes: 'Initial consultation',
        }
      });

      expect(appointment.patientId).toBe(patientRecord.id);
      expect(appointment.primaryProviderId).toBe(dentist.id);
      expect(appointment.status).toBe('SCHEDULED');

      // Step 2: Create case sheet (single case sheet per patient as per PRD)
      const caseSheet = await prisma.caseSheet.create({
        data: {
          patientId: patientRecord.id,
          clinicalNotes: 'Tooth pain in upper right quadrant',
          medicalHistory: 'No significant medical history',
          dentalHistory: 'Regular cleanings, no previous major work',
        }
      });

      expect(caseSheet.patientId).toBe(patientRecord.id);
      expect(caseSheet.clinicalNotes).toBe('Tooth pain in upper right quadrant');

      // Step 3: Create teeth for examination
      const tooth1 = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 3,
          quadrant: 1,
          positionInQuadrant: 3,
          toothName: 'Upper Right Canine',
        }
      });

      const tooth2 = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 4,
          quadrant: 1,
          positionInQuadrant: 4,
          toothName: 'Upper Right First Premolar',
        }
      });

      // Step 4: Record findings (simplified, no complex categorization)
      const finding1 = await prisma.finding.create({
        data: {
          
          tenantId: tenant.id,toothId: tooth1.id,
          description: 'Large carious lesion on occlusal surface',
          recordedById: dentist.id,
          recordedDate: new Date(),
        }
      });

      const finding2 = await prisma.finding.create({
        data: {
          
          tenantId: tenant.id,toothId: tooth2.id,
          description: 'Small carious lesion on mesial surface',
          recordedById: dentist.id,
          recordedDate: new Date(),
        }
      });

      expect(finding1.description).toBe('Large carious lesion on occlusal surface');
      expect(finding1.recordedById).toBe(dentist.id);
      expect(finding2.toothId).toBe(tooth2.id);

      // Step 5: Create treatments directly from findings (no diagnosis layer)
      const treatment1 = await prisma.treatment.create({
        data: {
          findingId: finding1.id,
          procedureName: 'Composite Restoration - Large',
          cost: 250.00,
          status: 'PENDING',
        }
      });

      const treatment2 = await prisma.treatment.create({
        data: {
          findingId: finding2.id,
          procedureName: 'Composite Restoration - Small',
          cost: 150.00,
          status: 'PENDING',
        }
      });

      expect(treatment1.findingId).toBe(finding1.id);
      expect(treatment1.cost).toBe(250.00);
      expect(treatment1.status).toBe('PENDING');

      // Step 6: Complete first treatment
      const completedTreatment1 = await prisma.treatment.update({
        where: { id: treatment1.id },
        data: {
          status: 'COMPLETED',
          completedDate: new Date(),
          completedById: dentist.id,
        }
      });

      expect(completedTreatment1.status).toBe('COMPLETED');
      expect(completedTreatment1.completedById).toBe(dentist.id);
      expect(completedTreatment1.completedDate).toBeInstanceOf(Date);

      // Step 7: Create invoice for completed treatments
      const invoice = await prisma.invoice.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceNumber: `INV-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 250.00, // Only completed treatment
          amountPaid: 0.00,
          balanceDue: 250.00,
          status: 'DRAFT',
        }
      });

      expect(invoice.patientId).toBe(patientRecord.id);
      expect(invoice.totalAmount).toBe(250.00);
      expect(invoice.balanceDue).toBe(250.00);
      expect(invoice.status).toBe('DRAFT');

      // Step 8: Send invoice
      const sentInvoice = await prisma.invoice.update({
        where: { id: invoice.id },
        data: { status: 'SENT' }
      });

      expect(sentInvoice.status).toBe('SENT');

      // Step 9: Record partial payment
      const payment1 = await prisma.payment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'CASH',
          status: 'COMPLETED',
          notes: 'Partial payment - cash',
        }
      });

      expect(payment1.amount).toBe(100.00);
      expect(payment1.paymentMethod).toBe('CASH');
      expect(payment1.status).toBe('COMPLETED');

      // Step 10: Update invoice with payment
      const updatedInvoice1 = await prisma.invoice.update({
        where: { id: invoice.id },
        data: {
          amountPaid: 100.00,
          balanceDue: 150.00,
        }
      });

      expect(updatedInvoice1.amountPaid).toBe(100.00);
      expect(updatedInvoice1.balanceDue).toBe(150.00);

      // Step 11: Record final payment
      const payment2 = await prisma.payment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 150.00,
          paymentDate: new Date(),
          paymentMethod: 'CARD',
          status: 'COMPLETED',
          notes: 'Final payment - credit card',
        }
      });

      // Step 12: Mark invoice as paid
      const paidInvoice = await prisma.invoice.update({
        where: { id: invoice.id },
        data: {
          amountPaid: 250.00,
          balanceDue: 0.00,
          status: 'PAID',
        }
      });

      expect(paidInvoice.status).toBe('PAID');
      expect(paidInvoice.balanceDue).toBe(0.00);

      // Step 13: Complete second treatment and create second invoice
      const completedTreatment2 = await prisma.treatment.update({
        where: { id: treatment2.id },
        data: {
          status: 'COMPLETED',
          completedDate: new Date(),
          completedById: dentist.id,
        }
      });

      const invoice2 = await prisma.invoice.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceNumber: `INV-${Date.now() + 1}`,
          invoiceDate: new Date(),
          totalAmount: 150.00,
          amountPaid: 0.00,
          balanceDue: 150.00,
          status: 'SENT',
        }
      });

      // Step 14: Verify complete workflow data integrity
      const workflowData = await prisma.patient.findUnique({
        where: { id: patientRecord.id },
        include: {
          caseSheets: {
            include: {
              teeth: {
                include: {
                  findings: {
                    include: {
                      treatments: true
                    }
                  }
                }
              }
            }
          },
          appointments: true,
          invoices: {
            include: {
              payments: true
            }
          },
          payments: true,
        }
      });

      // Verify patient data
      expect(workflowData).toBeTruthy();
      expect(workflowData!.firstName).toBe('Alice');

      // Verify case sheet (single per patient)
      expect(workflowData!.caseSheets).toHaveLength(1);
      expect(workflowData!.caseSheets[0].clinicalNotes).toBe('Tooth pain in upper right quadrant');

      // Verify teeth
      expect(workflowData!.caseSheets[0].teeth).toHaveLength(2);

      // Verify findings
      const allFindings = workflowData!.caseSheets[0].teeth.flatMap(t => t.findings);
      expect(allFindings).toHaveLength(2);

      // Verify treatments (direct from findings)
      const allTreatments = allFindings.flatMap(f => f.treatments);
      expect(allTreatments).toHaveLength(2);
      expect(allTreatments.every(t => t.status === 'COMPLETED')).toBe(true);

      // Verify appointments
      expect(workflowData!.appointments).toHaveLength(1);
      expect(workflowData!.appointments[0].appointmentType).toBe('CONSULTATION');

      // Verify invoices
      expect(workflowData!.invoices).toHaveLength(2);
      expect(workflowData!.invoices.find(i => i.status === 'PAID')).toBeTruthy();

      // Verify payments
      expect(workflowData!.payments).toHaveLength(2);
      expect(workflowData!.payments.every(p => p.status === 'COMPLETED')).toBe(true);

      // Verify total amounts
      const totalTreatmentCost = allTreatments.reduce((sum, t) => sum + Number(t.cost), 0);
      const totalInvoiceAmount = workflowData!.invoices.reduce((sum, i) => sum + Number(i.totalAmount), 0);
      const totalPaymentAmount = workflowData!.payments.reduce((sum, p) => sum + Number(p.amount), 0);

      expect(totalTreatmentCost).toBe(400.00);
      expect(totalInvoiceAmount).toBe(400.00);
      expect(totalPaymentAmount).toBe(250.00); // Only first invoice paid
    });
  });

  describe('Simplified Appointment Workflow', () => {
    it('should handle simplified appointment types and statuses', async () => {
      // Test all simplified appointment types
      const consultation = await prisma.appointment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          appointmentDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          appointmentType: 'CONSULTATION',
          status: 'SCHEDULED',
        }
      });

      const treatment = await prisma.appointment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          appointmentDate: new Date(Date.now() + 48 * 60 * 60 * 1000),
          appointmentType: 'TREATMENT',
          status: 'SCHEDULED',
        }
      });

      const checkup = await prisma.appointment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          appointmentDate: new Date(Date.now() + 72 * 60 * 60 * 1000),
          appointmentType: 'CHECKUP',
          status: 'SCHEDULED',
        }
      });

      expect(consultation.appointmentType).toBe('CONSULTATION');
      expect(treatment.appointmentType).toBe('TREATMENT');
      expect(checkup.appointmentType).toBe('CHECKUP');

      // Test status transitions
      const completedAppointment = await prisma.appointment.update({
        where: { id: consultation.id },
        data: { status: 'COMPLETED' }
      });

      const cancelledAppointment = await prisma.appointment.update({
        where: { id: treatment.id },
        data: { status: 'CANCELLED' }
      });

      expect(completedAppointment.status).toBe('COMPLETED');
      expect(cancelledAppointment.status).toBe('CANCELLED');
    });
  });

  describe('Simplified Payment Workflow', () => {
    it('should handle simplified payment methods and statuses', async () => {
      const invoice = await TestDataFactory.createTestInvoice(tenant.id, patientRecord.id, {
        totalAmount: 500.00,
        balanceDue: 500.00,
      });

      // Test all simplified payment methods
      const cashPayment = await prisma.payment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'CASH',
          status: 'COMPLETED',
        }
      });

      const cardPayment = await prisma.payment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 150.00,
          paymentDate: new Date(),
          paymentMethod: 'CARD',
          status: 'COMPLETED',
        }
      });

      const checkPayment = await prisma.payment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'CHECK',
          status: 'PENDING',
        }
      });

      const bankTransferPayment = await prisma.payment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'BANK_TRANSFER',
          status: 'COMPLETED',
        }
      });

      const otherPayment = await prisma.payment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 50.00,
          paymentDate: new Date(),
          paymentMethod: 'OTHER',
          status: 'COMPLETED',
        }
      });

      expect(cashPayment.paymentMethod).toBe('CASH');
      expect(cardPayment.paymentMethod).toBe('CARD');
      expect(checkPayment.paymentMethod).toBe('CHECK');
      expect(bankTransferPayment.paymentMethod).toBe('BANK_TRANSFER');
      expect(otherPayment.paymentMethod).toBe('OTHER');

      // Test payment status handling
      expect(checkPayment.status).toBe('PENDING');
      
      const processedCheck = await prisma.payment.update({
        where: { id: checkPayment.id },
        data: { status: 'COMPLETED' }
      });

      expect(processedCheck.status).toBe('COMPLETED');

      // Test failed payment
      const failedPayment = await prisma.payment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          amount: 25.00,
          paymentDate: new Date(),
          paymentMethod: 'CARD',
          status: 'FAILED',
          notes: 'Card declined',
        }
      });

      expect(failedPayment.status).toBe('FAILED');
    });
  });

  describe('Simplified Invoice Workflow', () => {
    it('should handle simplified invoice statuses', async () => {
      // Create draft invoice
      const draftInvoice = await prisma.invoice.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceNumber: `DRAFT-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 200.00,
          amountPaid: 0.00,
          balanceDue: 200.00,
          status: 'DRAFT',
        }
      });

      expect(draftInvoice.status).toBe('DRAFT');

      // Send invoice
      const sentInvoice = await prisma.invoice.update({
        where: { id: draftInvoice.id },
        data: { status: 'SENT' }
      });

      expect(sentInvoice.status).toBe('SENT');

      // Mark as paid
      const paidInvoice = await prisma.invoice.update({
        where: { id: draftInvoice.id },
        data: {
          status: 'PAID',
          amountPaid: 200.00,
          balanceDue: 0.00,
        }
      });

      expect(paidInvoice.status).toBe('PAID');
      expect(paidInvoice.balanceDue).toBe(0.00);

      // Test overdue invoice
      const overdueInvoice = await prisma.invoice.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceNumber: `OVERDUE-${Date.now()}`,
          invoiceDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
          totalAmount: 300.00,
          amountPaid: 0.00,
          balanceDue: 300.00,
          status: 'OVERDUE',
        }
      });

      expect(overdueInvoice.status).toBe('OVERDUE');
    });
  });

  describe('Audit Trail Verification', () => {
    it('should maintain audit trails throughout the workflow', async () => {
      setCurrentUserId(staff.id);

      // Create records with staff user
      const caseSheet = await prisma.caseSheet.create({
        data: {
          tenantId: tenant.id,
          patientId: patientRecord.id,
          clinicalNotes: 'Test complaint',
        }
      });

      const tooth = await prisma.tooth.create({
        data: {
          tenantId: tenant.id,
          caseSheetId: caseSheet.id,
          toothNumber: 1,
          quadrant: 1,
          positionInQuadrant: 1,
          toothName: 'Upper Right Central Incisor',
        }
      });

      // Switch to dentist for clinical work
      setCurrentUserId(dentist.id);

      const finding = await prisma.finding.create({
        data: {
          
          tenantId: tenant.id,toothId: tooth.id,
          description: 'Clinical finding',
          recordedById: dentist.id,
        }
      });

      const treatment = await prisma.treatment.create({
        data: {
          findingId: finding.id,
          procedureName: 'Test procedure',
          cost: 100.00,
        }
      });

      // Switch back to staff for billing
      setCurrentUserId(staff.id);

      const invoice = await prisma.invoice.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceNumber: `AUDIT-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 100.00,
          balanceDue: 100.00,
        }
      });

      const payment = await prisma.payment.create({
        data: {
          
          tenantId: tenant.id,patientId: patientRecord.id,
          invoiceId: invoice.id,
          amount: 100.00,
          paymentDate: new Date(),
          paymentMethod: 'CASH',
        }
      });

      // Verify audit trails
      expect(caseSheet.createdById).toBe(staff.id);
      expect(tooth.createdById).toBe(staff.id);
      expect(finding.createdById).toBe(dentist.id);
      expect(treatment.createdById).toBe(dentist.id);
      expect(invoice.createdById).toBe(staff.id);
      expect(payment.createdById).toBe(staff.id);

      // All should have audit timestamps
      const records = [caseSheet, tooth, finding, treatment, invoice, payment];
      records.forEach(record => {
        expect(record.createdAt).toBeInstanceOf(Date);
        expect(record.updatedAt).toBeInstanceOf(Date);
        expect(record.updatedById).toBeTruthy();
      });
    });
  });

  describe('Multitenancy in Workflows', () => {
    it('should maintain tenant isolation throughout workflows', async () => {
      // Create second tenant and switch context
      const tenant2 = await TestDataFactory.createTestTenant();
      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });

      // Create patient in second tenant
      const patient2User = await prisma.user.createPatientUser({
        tenantId: tenant2.id,
        phoneNumber: '+**********',
        password: 'password123',
        firstName: 'Bob',
        lastName: 'Wilson',
      });

      const patient2 = await prisma.patient.create({
        data: {
          tenantId: tenant2.id,
          userId: patient2User.id,
          firstName: 'Bob',
          lastName: 'Wilson',
          phoneNumber: '+**********',
          dateOfBirth: new Date('1985-01-01'),
        }
      });

      // Create workflow in second tenant
      const caseSheet2 = await prisma.caseSheet.create({
        data: {
          tenantId: tenant2.id,
          patientId: patient2.id,
          clinicalNotes: 'Tenant 2 complaint',
        }
      });

      const invoice2 = await prisma.invoice.create({
        data: {
          tenantId: tenant2.id,
          patientId: patient2.id,
          invoiceNumber: `T2-${Date.now()}`,
          invoiceDate: new Date(),
          totalAmount: 150.00,
          balanceDue: 150.00,
        }
      });

      // Switch back to first tenant
      TenantContextManager.setGlobalContext({ tenantId: tenant.id });

      // Verify tenant isolation
      const tenant1Patients = await prisma.patient.findMany();
      const tenant1CaseSheets = await prisma.caseSheet.findMany();
      const tenant1Invoices = await prisma.invoice.findMany();

      expect(tenant1Patients).toHaveLength(1);
      expect(tenant1Patients[0].id).toBe(patientRecord.id);

      expect(tenant1CaseSheets).toHaveLength(0); // None created in tenant1 in this test
      expect(tenant1Invoices).toHaveLength(0);   // None created in tenant1 in this test

      // Switch to second tenant and verify
      TenantContextManager.setGlobalContext({ tenantId: tenant2.id });

      const tenant2Patients = await prisma.patient.findMany();
      const tenant2CaseSheets = await prisma.caseSheet.findMany();
      const tenant2Invoices = await prisma.invoice.findMany();

      expect(tenant2Patients).toHaveLength(1);
      expect(tenant2Patients[0].id).toBe(patient2.id);

      expect(tenant2CaseSheets).toHaveLength(1);
      expect(tenant2CaseSheets[0].clinicalNotes).toBe('Tenant 2 complaint');

      expect(tenant2Invoices).toHaveLength(1);
      expect(tenant2Invoices[0].invoiceNumber).toContain('T2-');
    });
  });
});