import { Prisma, InvoiceStatus } from "@prisma/client";

interface CreateInvoiceInput {
  patientId: number;
  invoiceDate: Date;
  totalAmount?: number;
  tenantId: string;
  createdById?: number;
}

export const invoiceExtension = Prisma.defineExtension({
  name: "invoiceExtension",
  model: {
    invoice: {
      // Generate unique invoice number
      async generateInvoiceNumber(tenantId: string): Promise<string> {
        const today = new Date();
        const year = today.getFullYear();
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const day = today.getDate().toString().padStart(2, '0');
        const dateStr = `${year}${month}${day}`;
        
        // Find the highest invoice number for today
        const lastInvoice = await this.findFirst({
          where: {
            tenantId,
            invoiceNumber: {
              startsWith: `INV${dateStr}`,
            },
          },
          orderBy: {
            invoiceNumber: 'desc',
          },
        });

        let sequence = 1;
        if (lastInvoice) {
          const lastSequence = parseInt(lastInvoice.invoiceNumber.slice(-3));
          sequence = lastSequence + 1;
        }

        return `INV${dateStr}${sequence.toString().padStart(3, '0')}`;
      },

      // Create invoice with auto-generated invoice number
      async createInvoice(data: CreateInvoiceInput) {
        const invoiceNumber = await this.generateInvoiceNumber(data.tenantId);
        const totalAmount = data.totalAmount || 0;

        return this.create({
          data: {
            ...data,
            invoiceNumber,
            totalAmount,
            balanceDue: totalAmount,
            status: InvoiceStatus.DRAFT,
          },
        });
      },

      // Update invoice amount and recalculate balance
      async updateInvoiceAmount(
        invoiceId: number,
        totalAmount: number,
        updatedById?: number
      ) {
        const invoice = await this.findUnique({
          where: { id: invoiceId },
        });

        if (!invoice) {
          throw new Error('Invoice not found');
        }

        const newBalanceDue = totalAmount - invoice.amountPaid;

        return this.update({
          where: { id: invoiceId },
          data: {
            totalAmount,
            balanceDue: newBalanceDue,
            updatedById,
          },
        });
      },

      // Update amount paid and recalculate balance
      async updateAmountPaid(
        invoiceId: number,
        amountPaid: number,
        updatedById?: number
      ) {
        const invoice = await this.findUnique({
          where: { id: invoiceId },
        });

        if (!invoice) {
          throw new Error('Invoice not found');
        }

        const newBalanceDue = invoice.totalAmount - amountPaid;
        let newStatus = invoice.status;

        // Update status based on payment
        if (amountPaid >= invoice.totalAmount) {
          newStatus = InvoiceStatus.PAID;
        }

        return this.update({
          where: { id: invoiceId },
          data: {
            amountPaid,
            balanceDue: newBalanceDue,
            status: newStatus,
            updatedById,
          },
        });
      },

      // Update invoice status with basic validation
      async updateInvoiceStatus(
        invoiceId: number,
        newStatus: InvoiceStatus,
        updatedById?: number
      ) {
        const invoice = await this.findUnique({
          where: { id: invoiceId },
        });

        if (!invoice) {
          throw new Error('Invoice not found');
        }

        // Basic status validation - prevent invalid transitions
        if (invoice.status === InvoiceStatus.PAID && newStatus !== InvoiceStatus.PAID) {
          throw new Error('Cannot change status of a paid invoice');
        }

        return this.update({
          where: { id: invoiceId },
          data: {
            status: newStatus,
            updatedById,
          },
        });
      },

      // Get invoices by status
      async getInvoicesByStatus(tenantId: string, status: InvoiceStatus) {
        return this.findMany({
          where: {
            tenantId,
            status,
          },
          include: {
            patient: true,
          },
          orderBy: {
            invoiceDate: 'desc',
          },
        });
      },

      // Get overdue invoices
      async getOverdueInvoices(tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            status: InvoiceStatus.OVERDUE,
            balanceDue: {
              gt: 0,
            },
          },
          include: {
            patient: true,
          },
          orderBy: {
            invoiceDate: 'desc',
          },
        });
      },

      // Get invoices for a patient
      async getInvoicesForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            patientId,
          },
          include: {
            payments: true,
          },
          orderBy: {
            invoiceDate: 'desc',
          },
        });
      },

      // Mark invoice as sent
      async markInvoiceAsSent(
        invoiceId: number,
        updatedById?: number
      ) {
        return this.update({
          where: { id: invoiceId },
          data: {
            status: InvoiceStatus.SENT,
            updatedById,
          },
        });
      },

      // Validate invoice data
      async validateInvoice(data: Partial<CreateInvoiceInput & { id?: number }>) {
        const errors: string[] = [];

        // Validate required fields
        if (!data.patientId) {
          errors.push('Patient is required');
        }

        if (!data.invoiceDate) {
          errors.push('Invoice date is required');
        }

        if (!data.tenantId) {
          errors.push('Tenant ID is required');
        }

        // Validate amounts
        if (data.totalAmount !== undefined && data.totalAmount < 0) {
          errors.push('Total amount cannot be negative');
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },



      // Get basic invoice summary statistics
      async getInvoiceSummary(tenantId: string) {
        const invoices = await this.findMany({
          where: { tenantId },
        });

        const summary = {
          totalInvoices: invoices.length,
          totalAmount: 0,
          totalPaid: 0,
          totalOutstanding: 0,
          statusBreakdown: {} as Record<InvoiceStatus, { count: number; amount: number }>,
        };

        // Initialize status breakdown
        Object.values(InvoiceStatus).forEach(status => {
          summary.statusBreakdown[status] = { count: 0, amount: 0 };
        });

        invoices.forEach(invoice => {
          const totalAmount = Number(invoice.totalAmount);
          const amountPaid = Number(invoice.amountPaid);
          const balanceDue = Number(invoice.balanceDue);

          summary.totalAmount += totalAmount;
          summary.totalPaid += amountPaid;
          summary.totalOutstanding += balanceDue;

          summary.statusBreakdown[invoice.status].count++;
          summary.statusBreakdown[invoice.status].amount += totalAmount;
        });

        return summary;
      },
    },
  },
  result: {
    invoice: {
      // Check if invoice is overdue
      isOverdue: {
        needs: { status: true },
        compute(invoice) {
          return invoice.status === InvoiceStatus.OVERDUE;
        },
      },

      // Check if invoice is paid in full
      isPaidInFull: {
        needs: { balanceDue: true },
        compute(invoice) {
          return Number(invoice.balanceDue) <= 0;
        },
      },

      // Get payment percentage
      paymentPercentage: {
        needs: { totalAmount: true, amountPaid: true },
        compute(invoice) {
          const totalAmount = Number(invoice.totalAmount);
          const amountPaid = Number(invoice.amountPaid);
          
          if (totalAmount === 0) return 0;
          return Math.round((amountPaid / totalAmount) * 100);
        },
      },

      // Get status color for UI
      statusColor: {
        needs: { status: true },
        compute(invoice) {
          switch (invoice.status) {
            case InvoiceStatus.DRAFT:
              return 'gray';
            case InvoiceStatus.SENT:
              return 'blue';
            case InvoiceStatus.PAID:
              return 'green';
            case InvoiceStatus.OVERDUE:
              return 'red';
            default:
              return 'gray';
          }
        },
      },

      // Format invoice number for display
      displayInvoiceNumber: {
        needs: { invoiceNumber: true },
        compute(invoice) {
          return `#${invoice.invoiceNumber}`;
        },
      },

      // Check if invoice can be edited
      canBeEdited: {
        needs: { status: true },
        compute(invoice) {
          return invoice.status === InvoiceStatus.DRAFT;
        },
      },
    },
  },
});