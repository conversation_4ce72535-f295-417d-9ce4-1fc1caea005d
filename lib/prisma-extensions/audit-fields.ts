import { Prisma } from "@prisma/client";
import { TenantContextManager } from "../tenant-context";

// Interface for audit field data
export interface AuditFieldData {
  createdById?: number | null;
  updatedById?: number | null;
  createdAt?: Date;
  updatedAt?: Date;
}

// Interface for create operations with audit fields
export interface CreateWithAudit<T> {
  data: T & {
    createdById?: number | null;
    updatedById?: number | null;
  };
}

// Interface for update operations with audit fields
export interface UpdateWithAudit<T> {
  data: T & {
    updatedById?: number | null;
  };
}

// Helper function to get current user ID from context
// This would typically be set by authentication middleware
let currentUserId: number | null = null;

export function setCurrentUserId(userId: number | null) {
  currentUserId = userId;
}

export function getCurrentUserId(): number | null {
  return currentUserId;
}

// Helper function to inject audit fields for create operations
export function injectCreateAuditFields<T extends Record<string, any>>(
  data: T,
  providedCreatedById?: number | null
): T & AuditFieldData {
  const now = new Date();
  const createdById = providedCreatedById ?? getCurrentUserId();
  
  return {
    ...data,
    createdById,
    updatedById: createdById,
    createdAt: now,
    updatedAt: now,
  };
}

// Helper function to inject audit fields for update operations
export function injectUpdateAuditFields<T extends Record<string, any>>(
  data: T,
  providedUpdatedById?: number | null
): T & { updatedById?: number | null; updatedAt: Date } {
  const now = new Date();
  const updatedById = providedUpdatedById ?? getCurrentUserId();
  
  return {
    ...data,
    updatedById,
    updatedAt: now,
  };
}

// Models that have audit fields (exclude Tenant which doesn't have them)
const MODELS_WITH_AUDIT_FIELDS = new Set([
  'User', 'Patient', 'CaseSheet', 'Tooth', 'Finding', 
  'Treatment', 'Appointment', 'Invoice', 'Payment'
]);

// Extension that adds audit field handling to models with audit fields
export const auditFieldsExtension = Prisma.defineExtension({
  name: "auditFields",
  query: {
    $allModels: {
      // Automatically inject audit fields on create
      async create({ model, args, query }) {
        // Only inject if the model has audit fields
        if (MODELS_WITH_AUDIT_FIELDS.has(model) && args.data && typeof args.data === 'object') {
          const data = args.data as Record<string, any>;
          const auditedData = injectCreateAuditFields(data, data.createdById);
          
          return query({
            ...args,
            data: auditedData,
          });
        }
        
        return query(args);
      },

      // Automatically inject audit fields on createMany
      async createMany({ model, args, query }) {
        if (MODELS_WITH_AUDIT_FIELDS.has(model) && args.data && Array.isArray(args.data)) {
          const auditedData = args.data.map((item: any) => 
            injectCreateAuditFields(item, item.createdById)
          );
          
          return query({
            ...args,
            data: auditedData,
          });
        }
        
        return query(args);
      },

      // Automatically inject audit fields on update
      async update({ model, args, query }) {
        if (MODELS_WITH_AUDIT_FIELDS.has(model) && args.data && typeof args.data === 'object') {
          const data = args.data as Record<string, any>;
          const auditedData = injectUpdateAuditFields(data, data.updatedById);
          
          return query({
            ...args,
            data: auditedData,
          });
        }
        
        return query(args);
      },

      // Automatically inject audit fields on updateMany
      async updateMany({ model, args, query }) {
        if (MODELS_WITH_AUDIT_FIELDS.has(model) && args.data && typeof args.data === 'object') {
          const data = args.data as Record<string, any>;
          const auditedData = injectUpdateAuditFields(data, data.updatedById);
          
          return query({
            ...args,
            data: auditedData,
          });
        }
        
        return query(args);
      },

      // Automatically inject audit fields on upsert
      async upsert({ model, args, query }) {
        if (!MODELS_WITH_AUDIT_FIELDS.has(model)) {
          return query(args);
        }
        
        const updatedArgs = { ...args };
        
        if (args.create && typeof args.create === 'object') {
          const createData = args.create as Record<string, any>;
          updatedArgs.create = injectCreateAuditFields(createData, createData.createdById);
        }
        
        if (args.update && typeof args.update === 'object') {
          const updateData = args.update as Record<string, any>;
          updatedArgs.update = injectUpdateAuditFields(updateData, updateData.updatedById);
        }
        
        return query(updatedArgs);
      },
    },
  },
});

// Utility functions for manual audit field management
export const AuditUtils = {
  // Create audit fields for manual use
  createAuditFields(createdById?: number | null): AuditFieldData {
    const now = new Date();
    const userId = createdById ?? getCurrentUserId();
    
    return {
      createdById: userId,
      updatedById: userId,
      createdAt: now,
      updatedAt: now,
    };
  },

  // Update audit fields for manual use
  updateAuditFields(updatedById?: number | null): Pick<AuditFieldData, 'updatedById' | 'updatedAt'> {
    const now = new Date();
    const userId = updatedById ?? getCurrentUserId();
    
    return {
      updatedById: userId,
      updatedAt: now,
    };
  },

  // Validate audit fields
  validateAuditFields(data: Partial<AuditFieldData>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for required audit fields in create operations
    if (data.createdAt && !data.createdById) {
      errors.push('createdById is required when createdAt is present');
    }

    if (data.updatedAt && !data.updatedById) {
      errors.push('updatedById is required when updatedAt is present');
    }

    // Check for logical consistency
    if (data.createdAt && data.updatedAt && data.createdAt > data.updatedAt) {
      errors.push('createdAt cannot be after updatedAt');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  // Get audit trail information
  getAuditTrail(record: AuditFieldData): {
    created: { by: number | null; at: Date | undefined };
    updated: { by: number | null; at: Date | undefined };
  } {
    return {
      created: {
        by: record.createdById ?? null,
        at: record.createdAt,
      },
      updated: {
        by: record.updatedById ?? null,
        at: record.updatedAt,
      },
    };
  },
};