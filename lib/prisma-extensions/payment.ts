import { Prisma, PaymentMethod, PaymentStatus } from "@prisma/client";

interface CreatePaymentInput {
  patientId: number;
  invoiceId?: number;
  paymentDate: Date;
  amount: number;
  paymentMethod: PaymentMethod;
  status?: PaymentStatus;
  notes?: string;
  tenantId: string;
  createdById?: number;
}

export const paymentExtension = Prisma.defineExtension({
  name: "paymentExtension",
  model: {
    payment: {
      // Create payment with basic validation
      async createPayment(data: CreatePaymentInput) {
        return this.create({
          data: {
            ...data,
            status: data.status || PaymentStatus.COMPLETED,
          },
        });
      },

      // Get payments by status
      async getPaymentsByStatus(tenantId: string, status: PaymentStatus) {
        return this.findMany({
          where: {
            tenantId,
            status,
          },
          include: {
            patient: true,
            invoice: true,
          },
          orderBy: {
            paymentDate: 'desc',
          },
        });
      },

      // Get payments for a patient
      async getPaymentsForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            patientId,
          },
          include: {
            invoice: true,
          },
          orderBy: {
            paymentDate: 'desc',
          },
        });
      },

      // Get payment summary statistics
      async getPaymentSummary(tenantId: string, startDate?: Date, endDate?: Date) {
        const whereClause: any = { tenantId };
        
        if (startDate || endDate) {
          whereClause.paymentDate = {};
          if (startDate) whereClause.paymentDate.gte = startDate;
          if (endDate) whereClause.paymentDate.lte = endDate;
        }

        const payments = await this.findMany({
          where: whereClause,
        });

        const summary = {
          totalPayments: payments.length,
          totalAmount: 0,
          methodBreakdown: {} as Record<PaymentMethod, { count: number; amount: number }>,
          statusBreakdown: {} as Record<PaymentStatus, { count: number; amount: number }>,
        };

        // Initialize breakdowns
        Object.values(PaymentMethod).forEach(method => {
          summary.methodBreakdown[method] = { count: 0, amount: 0 };
        });

        Object.values(PaymentStatus).forEach(status => {
          summary.statusBreakdown[status] = { count: 0, amount: 0 };
        });

        payments.forEach(payment => {
          const amount = Number(payment.amount);
          summary.totalAmount += amount;

          summary.methodBreakdown[payment.paymentMethod].count++;
          summary.methodBreakdown[payment.paymentMethod].amount += amount;

          summary.statusBreakdown[payment.status].count++;
          summary.statusBreakdown[payment.status].amount += amount;
        });

        return summary;
      },

      // Validate payment data
      async validatePayment(data: Partial<CreatePaymentInput & { id?: number }>) {
        const errors: string[] = [];

        // Validate required fields
        if (!data.patientId) {
          errors.push('Patient is required');
        }

        if (!data.paymentDate) {
          errors.push('Payment date is required');
        }

        if (!data.amount || data.amount <= 0) {
          errors.push('Payment amount must be greater than 0');
        }

        if (!data.paymentMethod) {
          errors.push('Payment method is required');
        }

        if (!data.tenantId) {
          errors.push('Tenant ID is required');
        }

        // Validate payment date is not too far in the future
        if (data.paymentDate) {
          const maxFutureDate = new Date();
          maxFutureDate.setDate(maxFutureDate.getDate() + 1);
          
          if (data.paymentDate > maxFutureDate) {
            errors.push('Payment date cannot be more than 1 day in the future');
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Get payments by date range
      async getPaymentsByDateRange(
        tenantId: string,
        startDate: Date,
        endDate: Date,
        paymentMethod?: PaymentMethod
      ) {
        const where: any = {
          tenantId,
          paymentDate: {
            gte: startDate,
            lte: endDate,
          },
        };

        if (paymentMethod) {
          where.paymentMethod = paymentMethod;
        }

        return this.findMany({
          where,
          include: {
            patient: true,
            invoice: true,
          },
          orderBy: {
            paymentDate: 'desc',
          },
        });
      },

      // Get today's payments
      async getTodaysPayments(tenantId: string) {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

        return this.getPaymentsByDateRange(tenantId, startOfDay, endOfDay);
      },
    },
  },
  result: {
    payment: {
      // Check if payment was successful
      isSuccessful: {
        needs: { status: true },
        compute(payment) {
          return payment.status === PaymentStatus.COMPLETED;
        },
      },

      // Check if payment is electronic
      isElectronic: {
        needs: { paymentMethod: true },
        compute(payment) {
          const electronicMethods = [
            PaymentMethod.CARD,
            PaymentMethod.BANK_TRANSFER,
          ];
          return electronicMethods.includes(payment.paymentMethod);
        },
      },

      // Get user-friendly payment method display name
      paymentMethodDisplayName: {
        needs: { paymentMethod: true },
        compute(payment) {
          // Convert enum to display format
          return payment.paymentMethod.split('_').map(word => 
            word.charAt(0) + word.slice(1).toLowerCase()
          ).join(' ');
        },
      },

      // Get status color for UI
      statusColor: {
        needs: { status: true },
        compute(payment) {
          switch (payment.status) {
            case PaymentStatus.PENDING:
              return 'blue';
            case PaymentStatus.COMPLETED:
              return 'green';
            case PaymentStatus.FAILED:
              return 'red';
            default:
              return 'gray';
          }
        },
      },

      // Format amount for display
      displayAmount: {
        needs: { amount: true },
        compute(payment) {
          return `${Number(payment.amount).toFixed(2)}`;
        },
      },
    },
  },
});