import { Prisma, ToothStatus } from "@prisma/client";

interface CreateToothInput {
  caseSheetId: number;
  toothNumber: number;
  status?: ToothStatus;
  tenantId: string;
  createdById?: number;
}

interface UpdateToothInput {
  toothNumber?: number;
  status?: ToothStatus;
  updatedById?: number;
}

// FDI tooth naming mapping
const TOOTH_NAMES: Record<number, string> = {
  // Upper right quadrant (1)
  11: "Upper Right Central Incisor",
  12: "Upper Right Lateral Incisor", 
  13: "Upper Right Canine",
  14: "Upper Right First Premolar",
  15: "Upper Right Second Premolar",
  16: "Upper Right First Molar",
  17: "Upper Right Second Molar",
  18: "Upper Right Third Molar",
  
  // Upper left quadrant (2)
  21: "Upper Left Central Incisor",
  22: "Upper Left Lateral Incisor",
  23: "Upper Left Canine", 
  24: "Upper Left First Premolar",
  25: "Upper Left Second Premolar",
  26: "Upper Left First Molar",
  27: "Upper Left Second Molar",
  28: "Upper Left Third Molar",
  
  // Lower left quadrant (3)
  31: "Lower Left Central Incisor",
  32: "Lower Left Lateral Incisor",
  33: "Lower Left Canine",
  34: "Lower Left First Premolar", 
  35: "Lower Left Second Premolar",
  36: "Lower Left First Molar",
  37: "Lower Left Second Molar",
  38: "Lower Left Third Molar",
  
  // Lower right quadrant (4)
  41: "Lower Right Central Incisor",
  42: "Lower Right Lateral Incisor",
  43: "Lower Right Canine",
  44: "Lower Right First Premolar",
  45: "Lower Right Second Premolar", 
  46: "Lower Right First Molar",
  47: "Lower Right Second Molar",
  48: "Lower Right Third Molar",
};

export const toothExtension = Prisma.defineExtension({
  name: "toothExtension",
  model: {
    tooth: {
      // Populate tooth data from FDI notation
      populateToothDataFromFDI(toothNumber: number) {
        if (toothNumber < 11 || toothNumber > 48) {
          throw new Error('Invalid FDI tooth number. Must be between 11-48 for adult teeth.');
        }

        const quadrant = Math.floor(toothNumber / 10);
        const positionInQuadrant = toothNumber % 10;
        
        // Validate quadrant (1-4) and position (1-8)
        if (![1, 2, 3, 4].includes(quadrant) || positionInQuadrant < 1 || positionInQuadrant > 8) {
          throw new Error('Invalid FDI tooth number format.');
        }

        const toothName = TOOTH_NAMES[toothNumber];
        if (!toothName) {
          throw new Error(`Unknown tooth name for FDI number ${toothNumber}`);
        }

        return {
          quadrant,
          positionInQuadrant,
          toothName,
        };
      },

      // Get tooth name from FDI number
      getToothName(toothNumber: number): string {
        const toothName = TOOTH_NAMES[toothNumber];
        if (!toothName) {
          throw new Error(`Unknown tooth name for FDI number ${toothNumber}`);
        }
        return toothName;
      },

      // Create tooth with auto-populated FDI data
      async createTooth(data: CreateToothInput) {
        const fdiData = this.populateToothDataFromFDI(data.toothNumber);
        
        // Check if tooth already exists for this case sheet
        const existingTooth = await this.findFirst({
          where: {
            tenantId: data.tenantId,
            caseSheetId: data.caseSheetId,
            toothNumber: data.toothNumber,
          },
        });

        if (existingTooth) {
          throw new Error(`Tooth ${data.toothNumber} already exists for this case sheet`);
        }

        return this.create({
          data: {
            ...data,
            quadrant: fdiData.quadrant,
            positionInQuadrant: fdiData.positionInQuadrant,
            toothName: fdiData.toothName,
            status: data.status ?? ToothStatus.PRESENT,
          },
        });
      },

      // Create all 32 adult teeth for a case sheet
      async createTeethForCaseSheet(caseSheetId: number, tenantId: string, createdById?: number) {
        const allToothNumbers = [
          // Upper right (1)
          11, 12, 13, 14, 15, 16, 17, 18,
          // Upper left (2) 
          21, 22, 23, 24, 25, 26, 27, 28,
          // Lower left (3)
          31, 32, 33, 34, 35, 36, 37, 38,
          // Lower right (4)
          41, 42, 43, 44, 45, 46, 47, 48,
        ];

        // Check if teeth already exist for this case sheet
        const existingTeeth = await this.findMany({
          where: {
            tenantId,
            caseSheetId,
          },
        });

        if (existingTeeth.length > 0) {
          throw new Error('Teeth already exist for this case sheet');
        }

        const teethData = allToothNumbers.map(toothNumber => {
          const fdiData = this.populateToothDataFromFDI(toothNumber);
          return {
            tenantId,
            caseSheetId,
            toothNumber,
            quadrant: fdiData.quadrant,
            positionInQuadrant: fdiData.positionInQuadrant,
            toothName: fdiData.toothName,
            status: ToothStatus.PRESENT,
            createdById,
          };
        });

        return this.createMany({
          data: teethData,
        });
      },

      // Get teeth by case sheet
      async getTeethByCaseSheet(caseSheetId: number, tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            caseSheetId,
          },
          orderBy: {
            toothNumber: 'asc',
          },
        });
      },

      // Get teeth by quadrant
      async getTeethByQuadrant(caseSheetId: number, quadrant: number, tenantId: string) {
        if (![1, 2, 3, 4].includes(quadrant)) {
          throw new Error('Invalid quadrant. Must be 1, 2, 3, or 4.');
        }

        return this.findMany({
          where: {
            tenantId,
            caseSheetId,
            quadrant,
          },
          orderBy: {
            positionInQuadrant: 'asc',
          },
        });
      },

      // Get teeth by status
      async getTeethByStatus(caseSheetId: number, status: ToothStatus, tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            caseSheetId,
            status,
          },
          orderBy: {
            toothNumber: 'asc',
          },
        });
      },

      // Update tooth status
      async updateToothStatus(toothId: number, status: ToothStatus, updatedById?: number) {
        const tooth = await this.findUnique({
          where: { id: toothId },
        });

        if (!tooth) {
          throw new Error('Tooth not found');
        }

        return this.update({
          where: { id: toothId },
          data: {
            status,
            updatedById,
          },
        });
      },

      // Mark tooth as missing
      async markAsMissing(toothId: number, updatedById?: number) {
        return this.updateToothStatus(toothId, ToothStatus.MISSING, updatedById);
      },

      // Mark tooth as extracted
      async markAsExtracted(toothId: number, updatedById?: number) {
        return this.updateToothStatus(toothId, ToothStatus.EXTRACTED, updatedById);
      },

      // Mark tooth as having crown
      async markAsCrown(toothId: number, updatedById?: number) {
        return this.updateToothStatus(toothId, ToothStatus.CROWN, updatedById);
      },

      // Mark tooth as having filling
      async markAsFilling(toothId: number, updatedById?: number) {
        return this.updateToothStatus(toothId, ToothStatus.FILLING, updatedById);
      },

      // Mark tooth as present
      async markAsPresent(toothId: number, updatedById?: number) {
        return this.updateToothStatus(toothId, ToothStatus.PRESENT, updatedById);
      },

      // Get tooth by FDI number and case sheet
      async findByFDINumber(toothNumber: number, caseSheetId: number, tenantId: string) {
        return this.findFirst({
          where: {
            tenantId,
            caseSheetId,
            toothNumber,
          },
        });
      },

      // Get teeth with findings
      async getTeethWithFindings(caseSheetId: number, tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            caseSheetId,
            findings: {
              some: {},
            },
          },
          include: {
            findings: {
              orderBy: {
                recordedDate: 'desc',
              },
            },
          },
          orderBy: {
            toothNumber: 'asc',
          },
        });
      },

      // Get teeth without findings (healthy teeth)
      async getHealthyTeeth(caseSheetId: number, tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            caseSheetId,
            status: ToothStatus.PRESENT,
            findings: {
              none: {},
            },
          },
          orderBy: {
            toothNumber: 'asc',
          },
        });
      },

      // Get all quadrant names
      getQuadrantNames() {
        return {
          1: "Upper Right",
          2: "Upper Left", 
          3: "Lower Left",
          4: "Lower Right",
        };
      },

      // Get quadrant name by number
      getQuadrantName(quadrant: number): string {
        const quadrantNames = this.getQuadrantNames();
        const name = quadrantNames[quadrant as keyof typeof quadrantNames];
        if (!name) {
          throw new Error(`Invalid quadrant number: ${quadrant}`);
        }
        return name;
      },

      // Validate tooth number
      validateToothNumber(toothNumber: number): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!Number.isInteger(toothNumber)) {
          errors.push('Tooth number must be an integer');
        }

        if (toothNumber < 11 || toothNumber > 48) {
          errors.push('Tooth number must be between 11-48 for adult teeth');
        }

        const quadrant = Math.floor(toothNumber / 10);
        const position = toothNumber % 10;

        if (![1, 2, 3, 4].includes(quadrant)) {
          errors.push('Invalid quadrant in tooth number');
        }

        if (position < 1 || position > 8) {
          errors.push('Invalid position in quadrant (must be 1-8)');
        }

        if (!TOOTH_NAMES[toothNumber]) {
          errors.push('Unknown tooth number');
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Validate tooth data
      async validateTooth(data: Partial<CreateToothInput | UpdateToothInput>) {
        const errors: string[] = [];

        if (data.toothNumber !== undefined) {
          const validation = this.validateToothNumber(data.toothNumber);
          if (!validation.isValid) {
            errors.push(...validation.errors);
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
        };
      },

      // Clean method equivalent - comprehensive validation and updates
      async cleanTooth(toothId: number) {
        const tooth = await this.findUnique({
          where: { id: toothId },
        });

        if (!tooth) {
          throw new Error('Tooth not found');
        }

        const validation = await this.validateTooth({
          toothNumber: tooth.toothNumber,
        });

        if (!validation.isValid) {
          throw new Error(`Tooth validation failed: ${validation.errors.join(', ')}`);
        }

        // Ensure FDI data is correct
        const fdiData = this.populateToothDataFromFDI(tooth.toothNumber);
        
        // Update if FDI data is incorrect
        if (tooth.quadrant !== fdiData.quadrant || 
            tooth.positionInQuadrant !== fdiData.positionInQuadrant || 
            tooth.toothName !== fdiData.toothName) {
          
          return this.update({
            where: { id: toothId },
            data: {
              quadrant: fdiData.quadrant,
              positionInQuadrant: fdiData.positionInQuadrant,
              toothName: fdiData.toothName,
            },
          });
        }

        return tooth;
      },
    },
  },
  result: {
    tooth: {
      // Get quadrant display name
      quadrantDisplay: {
        needs: { quadrant: true },
        compute(tooth) {
          const quadrantNames = {
            1: "Upper Right",
            2: "Upper Left",
            3: "Lower Left", 
            4: "Lower Right",
          };
          return quadrantNames[tooth.quadrant as keyof typeof quadrantNames] || 'Unknown';
        },
      },

      // Get status display name
      statusDisplay: {
        needs: { status: true },
        compute(tooth) {
          switch (tooth.status) {
            case ToothStatus.PRESENT:
              return 'Present';
            case ToothStatus.MISSING:
              return 'Missing';
            case ToothStatus.EXTRACTED:
              return 'Extracted';
            case ToothStatus.CROWN:
              return 'Crown';
            case ToothStatus.FILLING:
              return 'Filling';
            default:
              return 'Unknown';
          }
        },
      },

      // Check if tooth is present
      isPresent: {
        needs: { status: true },
        compute(tooth) {
          return tooth.status === ToothStatus.PRESENT;
        },
      },

      // Check if tooth is missing
      isMissing: {
        needs: { status: true },
        compute(tooth) {
          return tooth.status === ToothStatus.MISSING;
        },
      },

      // Check if tooth is extracted
      isExtracted: {
        needs: { status: true },
        compute(tooth) {
          return tooth.status === ToothStatus.EXTRACTED;
        },
      },

      // Check if tooth has crown
      hasCrown: {
        needs: { status: true },
        compute(tooth) {
          return tooth.status === ToothStatus.CROWN;
        },
      },

      // Check if tooth has filling
      hasFilling: {
        needs: { status: true },
        compute(tooth) {
          return tooth.status === ToothStatus.FILLING;
        },
      },

      // Check if tooth is available for treatment (present, crown, or filling)
      isAvailableForTreatment: {
        needs: { status: true },
        compute(tooth) {
          return [ToothStatus.PRESENT, ToothStatus.CROWN, ToothStatus.FILLING].includes(tooth.status);
        },
      },

      // Get tooth type based on position
      toothType: {
        needs: { positionInQuadrant: true },
        compute(tooth) {
          switch (tooth.positionInQuadrant) {
            case 1:
            case 2:
              return 'Incisor';
            case 3:
              return 'Canine';
            case 4:
            case 5:
              return 'Premolar';
            case 6:
            case 7:
            case 8:
              return 'Molar';
            default:
              return 'Unknown';
          }
        },
      },

      // Check if tooth is anterior (front teeth)
      isAnterior: {
        needs: { positionInQuadrant: true },
        compute(tooth) {
          return tooth.positionInQuadrant <= 3; // Incisors and canines
        },
      },

      // Check if tooth is posterior (back teeth)
      isPosterior: {
        needs: { positionInQuadrant: true },
        compute(tooth) {
          return tooth.positionInQuadrant >= 4; // Premolars and molars
        },
      },

      // Check if tooth is wisdom tooth
      isWisdomTooth: {
        needs: { positionInQuadrant: true },
        compute(tooth) {
          return tooth.positionInQuadrant === 8;
        },
      },

      // Get tooth position description
      positionDescription: {
        needs: { toothNumber: true, quadrant: true, positionInQuadrant: true },
        compute(tooth) {
          const quadrantNames = {
            1: "Upper Right",
            2: "Upper Left", 
            3: "Lower Left",
            4: "Lower Right",
          };
          const quadrantName = quadrantNames[tooth.quadrant as keyof typeof quadrantNames];
          return `${quadrantName} - Position ${tooth.positionInQuadrant} (FDI: ${tooth.toothNumber})`;
        },
      },

      // Get short display name
      shortDisplay: {
        needs: { toothNumber: true, toothName: true },
        compute(tooth) {
          return `#${tooth.toothNumber}`;
        },
      },

      // Get full display name
      fullDisplay: {
        needs: { toothNumber: true, toothName: true },
        compute(tooth) {
          return `#${tooth.toothNumber} - ${tooth.toothName}`;
        },
      },

      // Get color code for UI display based on status
      statusColor: {
        needs: { status: true },
        compute(tooth) {
          switch (tooth.status) {
            case ToothStatus.PRESENT:
              return '#4CAF50'; // Green
            case ToothStatus.MISSING:
              return '#F44336'; // Red
            case ToothStatus.EXTRACTED:
              return '#FF9800'; // Orange
            case ToothStatus.CROWN:
              return '#2196F3'; // Blue
            case ToothStatus.FILLING:
              return '#9C27B0'; // Purple
            default:
              return '#757575'; // Gray
          }
        },
      },

      // Check if tooth needs attention (has findings)
      needsAttention: {
        needs: { findings: true },
        compute(tooth) {
          return tooth.findings && tooth.findings.length > 0;
        },
      },
    },
  },
});