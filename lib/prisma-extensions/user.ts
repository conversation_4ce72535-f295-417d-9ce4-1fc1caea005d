import { Prisma } from "@prisma/client";
import * as bcrypt from "bcryptjs";

// Simplified UserType enum values
enum UserType {
  ADMIN = "ADMIN",
  DENTIST = "DENTIST", 
  STAFF = "STAFF",
  PATIENT = "PATIENT"
}

interface CreateUserInput {
  username?: string;
  phoneNumber?: string;
  email?: string;
  password: string;
  firstName?: string;
  lastName?: string;
  userType?: UserType;
  isActive?: boolean;
  tenantId: string;
  createdById?: number;
}

export const userExtension = Prisma.defineExtension({
  name: "userExtension",
  model: {
    user: {
      // Simplified user creation
      async createUser(data: CreateUserInput) {
        const { password, userType = UserType.PATIENT, ...userData } = data;

        // Validation: Either username or phone number must be provided
        if (!userData.username && !userData.phoneNumber) {
          throw new Error('Either username or phone number must be provided');
        }

        // For patients, use phone_number as username if username is not provided
        if (!userData.username && userData.phoneNumber && userType === UserType.PATIENT) {
          userData.username = userData.phoneNumber;
        }

        // Normalize email if provided
        if (userData.email) {
          userData.email = userData.email.toLowerCase().trim();
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 12);

        return this.create({
          data: {
            ...userData,
            password: hashedPassword,
            userType,
            isActive: userData.isActive ?? true,
          },
        });
      },

      // Simplified user type filtering
      async getUsersByType(userType: UserType, tenantId: string) {
        return this.findMany({
          where: {
            tenantId,
            userType,
            isActive: true,
          },
          orderBy: [
            { lastName: 'asc' },
            { firstName: 'asc' },
          ],
        });
      },

      // Simplified user type creation methods
      async createAdminUser(data: Omit<CreateUserInput, 'userType'>) {
        if (!data.username) {
          throw new Error('Username is required for Admin users');
        }
        return this.createUser({ ...data, userType: UserType.ADMIN });
      },

      async createStaffUser(data: Omit<CreateUserInput, 'userType'>) {
        if (!data.username) {
          throw new Error('Username is required for Staff users');
        }
        return this.createUser({ ...data, userType: UserType.STAFF });
      },

      async createPatientUser(data: Omit<CreateUserInput, 'userType'>) {
        if (!data.phoneNumber) {
          throw new Error('Phone number is required for Patient users');
        }
        return this.createUser({ ...data, userType: UserType.PATIENT });
      },

      async createDentistUser(data: Omit<CreateUserInput, 'userType'>) {
        if (!data.username && !data.phoneNumber) {
          throw new Error('Either username or phone number is required for Dentist users');
        }
        return this.createUser({ ...data, userType: UserType.DENTIST });
      },

      // Simplified authentication
      async authenticate(identifier: string, password: string, tenantId: string) {
        try {
          const user = await this.findFirst({
            where: {
              tenantId,
              OR: [
                { username: identifier },
                { phoneNumber: identifier }
              ],
              isActive: true,
            },
          });

          if (!user) {
            return null;
          }

          const isPasswordValid = await bcrypt.compare(password, user.password);
          if (!isPasswordValid) {
            return null;
          }

          return user;
        } catch (error) {
          return null;
        }
      },

      // Simplified password change
      async changePassword(userId: number, currentPassword: string, newPassword: string) {
        const user = await this.findUnique({
          where: { id: userId },
        });

        if (!user) {
          throw new Error('User not found');
        }

        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
          throw new Error('Current password is incorrect');
        }

        // Basic password validation
        if (newPassword.length < 8) {
          throw new Error('Password must be at least 8 characters long');
        }

        // Hash new password
        const hashedNewPassword = await bcrypt.hash(newPassword, 12);

        // Update password
        return this.update({
          where: { id: userId },
          data: { password: hashedNewPassword },
        });
      },

      // Simplified user type update
      async updateUserType(userId: number, userType: UserType, updatedById?: number) {
        const user = await this.findUnique({
          where: { id: userId },
        });

        if (!user) {
          throw new Error('User not found');
        }

        // Basic validation based on user type
        if (userType === UserType.PATIENT && !user.phoneNumber) {
          throw new Error('Phone number is required for Patient users');
        }

        if ((userType === UserType.ADMIN || userType === UserType.STAFF) && !user.username) {
          throw new Error('Username is required for Admin and Staff users');
        }

        return this.update({
          where: { id: userId },
          data: {
            userType,
            updatedById,
          },
        });
      },

      // Simplified user lookup methods
      async findByIdentifier(identifier: string, tenantId: string) {
        return this.findFirst({
          where: {
            tenantId,
            OR: [
              { username: identifier },
              { phoneNumber: identifier }
            ],
            isActive: true,
          },
        });
      },
    },
  },
  result: {
    user: {
      // Simplified display name
      displayName: {
        needs: { 
          firstName: true, 
          lastName: true, 
          username: true, 
          phoneNumber: true, 
          userType: true 
        },
        compute(user) {
          const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
          const name = fullName || user.username || user.phoneNumber || 'Unknown User';
          
          if (user.userType === UserType.PATIENT && user.phoneNumber) {
            return `${name} (${user.phoneNumber})`;
          } else if (user.username) {
            return `${name} (${user.username})`;
          } else {
            return name;
          }
        },
      },

      // Check if user is clinical provider
      isClinicalProvider: {
        needs: { userType: true },
        compute(user) {
          return user.userType === UserType.DENTIST || user.userType === UserType.ADMIN;
        },
      },

      // Get user type display name
      userTypeDisplay: {
        needs: { userType: true },
        compute(user) {
          switch (user.userType) {
            case UserType.ADMIN:
              return 'Admin';
            case UserType.STAFF:
              return 'Staff';
            case UserType.PATIENT:
              return 'Patient';
            case UserType.DENTIST:
              return 'Dentist';
            default:
              return 'Unknown';
          }
        },
      },

      // Get primary authentication field
      primaryAuthField: {
        needs: { userType: true, username: true, phoneNumber: true },
        compute(user) {
          if (user.userType === UserType.PATIENT) {
            return user.phoneNumber;
          } else {
            return user.username;
          }
        },
      },
    },
  },
});
