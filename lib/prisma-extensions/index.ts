import { Prisma } from '@prisma/client';
import { multitenancyExtension } from './multitenancy';
import { auditFieldsExtension } from './audit-fields';
import { userExtension } from './user';
import { patientExtension } from './patient';
import { caseSheetExtension } from './case_sheet';
import { toothExtension } from './tooth';
import { findingExtension } from './finding';
import { treatmentExtension } from './treatment';
import { appointmentExtension } from './appointment';
import { invoiceExtension } from './invoice';
import { paymentExtension } from './payment';

// Export all extensions for easy importing
export { multitenancyExtension, TenantContext } from './multitenancy';
export { auditFieldsExtension, AuditUtils, setCurrentUserId, getCurrentUserId } from './audit-fields';
export { userExtension } from './user';
export { patientExtension } from './patient';
export { caseSheetExtension } from './case_sheet';
export { toothExtension } from './tooth';
export { findingExtension } from './finding';
export { treatmentExtension } from './treatment';
export { appointmentExtension } from './appointment';
export { invoiceExtension } from './invoice';
export { paymentExtension } from './payment';
export { TenantContextManager } from '../tenant-context';

// Extension type definitions for proper TypeScript support
export type MultitenancyExtension = typeof multitenancyExtension;
export type AuditFieldsExtension = typeof auditFieldsExtension;
export type UserExtension = typeof userExtension;
export type PatientExtension = typeof patientExtension;
export type CaseSheetExtension = typeof caseSheetExtension;
export type ToothExtension = typeof toothExtension;
export type FindingExtension = typeof findingExtension;
export type TreatmentExtension = typeof treatmentExtension;
export type AppointmentExtension = typeof appointmentExtension;
export type InvoiceExtension = typeof invoiceExtension;
export type PaymentExtension = typeof paymentExtension;

// Combined extensions array for easy application to Prisma client
// Order matters: multitenancy first, then audit fields, then model-specific extensions
export const allExtensions = [
  multitenancyExtension,
  auditFieldsExtension,
  userExtension,
  patientExtension,
  caseSheetExtension,
  toothExtension,
  findingExtension,
  treatmentExtension,
  appointmentExtension,
  invoiceExtension,
  paymentExtension,
] as const;

// Type for the combined extensions
export type AllExtensions = typeof allExtensions;

// Type for Prisma client with all extensions applied
export type ExtendedPrismaClient = Prisma.PrismaClient<
  Prisma.PrismaClientOptions,
  never,
  Prisma.DefaultArgs
> & 
  ReturnType<MultitenancyExtension['$extends']> &
  ReturnType<AuditFieldsExtension['$extends']> &
  ReturnType<UserExtension['$extends']> &
  ReturnType<PatientExtension['$extends']> &
  ReturnType<CaseSheetExtension['$extends']> &
  ReturnType<ToothExtension['$extends']> &
  ReturnType<FindingExtension['$extends']> &
  ReturnType<TreatmentExtension['$extends']> &
  ReturnType<AppointmentExtension['$extends']> &
  ReturnType<InvoiceExtension['$extends']> &
  ReturnType<PaymentExtension['$extends']>;

// Extension configuration interface
export interface ExtensionConfig {
  enableMultitenancy?: boolean;
  enableAuditFields?: boolean;
  enableUserExtension?: boolean;
  enablePatientExtension?: boolean;
  enableCaseSheetExtension?: boolean;
  enableToothExtension?: boolean;
  enableFindingExtension?: boolean;
  enableTreatmentExtension?: boolean;
  enableAppointmentExtension?: boolean;
  enableInvoiceExtension?: boolean;
  enablePaymentExtension?: boolean;
}

// Default configuration - all extensions enabled
export const defaultExtensionConfig: ExtensionConfig = {
  enableMultitenancy: true,
  enableAuditFields: true,
  enableUserExtension: true,
  enablePatientExtension: true,
  enableCaseSheetExtension: true,
  enableToothExtension: true,
  enableFindingExtension: true,
  enableTreatmentExtension: true,
  enableAppointmentExtension: true,
  enableInvoiceExtension: true,
  enablePaymentExtension: true,
};

// Function to create a filtered extensions array based on configuration
export function createExtensionsArray(config: ExtensionConfig = defaultExtensionConfig) {
  const extensions: any[] = [];
  
  // Always include multitenancy first as it's foundational
  if (config.enableMultitenancy !== false) {
    extensions.push(multitenancyExtension);
  }
  
  // Include audit fields extension second as it's also foundational
  if (config.enableAuditFields !== false) {
    extensions.push(auditFieldsExtension);
  }
  
  // Add model extensions based on configuration
  if (config.enableUserExtension !== false) {
    extensions.push(userExtension);
  }
  if (config.enablePatientExtension !== false) {
    extensions.push(patientExtension);
  }
  if (config.enableCaseSheetExtension !== false) {
    extensions.push(caseSheetExtension);
  }
  if (config.enableToothExtension !== false) {
    extensions.push(toothExtension);
  }
  if (config.enableFindingExtension !== false) {
    extensions.push(findingExtension);
  }
  if (config.enableTreatmentExtension !== false) {
    extensions.push(treatmentExtension);
  }
  if (config.enableAppointmentExtension !== false) {
    extensions.push(appointmentExtension);
  }
  if (config.enableInvoiceExtension !== false) {
    extensions.push(invoiceExtension);
  }
  if (config.enablePaymentExtension !== false) {
    extensions.push(paymentExtension);
  }
  
  return extensions;
}

// Utility function to apply all extensions to a Prisma client
export function applyAllExtensions(
  client: Prisma.PrismaClient,
  config: ExtensionConfig = defaultExtensionConfig
) {
  const extensions = createExtensionsArray(config);
  
  // Apply extensions in sequence
  let extendedClient = client;
  for (const extension of extensions) {
    extendedClient = extendedClient.$extends(extension);
  }
  
  return extendedClient as ExtendedPrismaClient;
}

// Extension dependency management
export const extensionDependencies = {
  multitenancy: [], // No dependencies
  auditFields: [], // No dependencies (foundational)
  user: ['multitenancy', 'auditFields'], // Depends on multitenancy and audit fields
  patient: ['multitenancy', 'auditFields', 'user'], // Depends on multitenancy, audit fields, and user
  caseSheet: ['multitenancy', 'auditFields', 'patient'], // Depends on multitenancy, audit fields, and patient
  tooth: ['multitenancy', 'auditFields', 'caseSheet'], // Depends on multitenancy, audit fields, and case sheet
  finding: ['multitenancy', 'auditFields', 'tooth', 'user'], // Depends on multitenancy, audit fields, tooth, and user
  treatment: ['multitenancy', 'auditFields', 'finding', 'user'], // Depends on multitenancy, audit fields, finding, and user
  appointment: ['multitenancy', 'auditFields', 'patient', 'user'], // Depends on multitenancy, audit fields, patient, and user
  invoice: ['multitenancy', 'auditFields', 'patient'], // Depends on multitenancy, audit fields, and patient
  payment: ['multitenancy', 'auditFields', 'patient', 'invoice', 'user'], // Depends on multitenancy, audit fields, patient, invoice, and user
} as const;

// Function to validate extension dependencies
export function validateExtensionDependencies(config: ExtensionConfig): string[] {
  const errors: string[] = [];
  const enabledExtensions = new Set<string>();
  
  // Build set of enabled extensions
  Object.entries(config).forEach(([key, enabled]) => {
    if (enabled !== false) {
      let extensionName = key.replace('enable', '').replace('Extension', '').toLowerCase();
      // Handle special cases
      if (extensionName === 'auditfields') {
        extensionName = 'auditFields';
      }
      if (extensionName === 'casesheet') {
        extensionName = 'caseSheet';
      }
      enabledExtensions.add(extensionName);
    }
  });
  
  // Check dependencies
  Object.entries(extensionDependencies).forEach(([extension, deps]) => {
    if (enabledExtensions.has(extension)) {
      deps.forEach(dep => {
        if (!enabledExtensions.has(dep)) {
          errors.push(`Extension '${extension}' requires '${dep}' to be enabled`);
        }
      });
    }
  });
  
  return errors;
}