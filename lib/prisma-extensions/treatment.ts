import { Prisma, TreatmentStatus } from "@prisma/client";

// Common procedures for basic treatment tracking
const COMMON_PROCEDURES = [
  { name: 'Prophylaxis - adult', averageCost: 100 },
  { name: 'Comprehensive oral evaluation', averageCost: 100 },
  { name: 'Periodic oral evaluation', averageCost: 80 },
  { name: 'Amalgam filling', averageCost: 150 },
  { name: 'Composite filling', averageCost: 200 },
  { name: 'Tooth extraction', averageCost: 150 },
  { name: 'Crown', averageCost: 1200 },
  { name: 'Root canal therapy', averageCost: 800 },
  { name: 'Scaling and root planing', averageCost: 300 },
  { name: 'Fluoride treatment', averageCost: 50 },
];

export const treatmentExtension = Prisma.defineExtension({
  name: "treatmentExtension",
  model: {
    treatment: {
      // Mark treatment as completed with provider and date tracking
      async markCompleted(treatmentId: number, completedById: number) {
        const now = new Date();
        
        return this.update({
          where: { id: treatmentId },
          data: {
            status: TreatmentStatus.COMPLETED,
            completedDate: now,
            completedById,
            updatedAt: now,
            updatedById: completedById,
          },
        });
      },

      // Get common procedures for treatment creation
      getCommonProcedures() {
        return COMMON_PROCEDURES;
      },

      // Get treatments for a patient
      async getTreatmentsForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            finding: {
              tooth: {
                caseSheet: {
                  patientId,
                },
              },
            },
          },
          include: {
            finding: {
              include: {
                tooth: true,
              },
            },
            completedBy: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
      },

      // Get treatments for a specific finding
      async getTreatmentsForFinding(tenantId: string, findingId: number) {
        return this.findMany({
          where: {
            tenantId,
            findingId,
          },
          include: {
            finding: true,
            completedBy: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
      },

      // Create treatment with basic validation
      async createTreatment(data: {
        tenantId: string;
        findingId: number;
        procedureName: string;
        cost: number;
        createdById?: number;
      }) {
        // Basic validation
        if (!data.procedureName.trim()) {
          throw new Error('Procedure name is required');
        }

        if (data.cost < 0) {
          throw new Error('Treatment cost cannot be negative');
        }

        return this.create({
          data: {
            tenantId: data.tenantId,
            findingId: data.findingId,
            procedureName: data.procedureName.trim(),
            cost: data.cost,
            createdById: data.createdById,
          },
          include: {
            finding: {
              include: {
                tooth: true,
              },
            },
          },
        });
      },

      // Update treatment cost
      async updateCost(treatmentId: number, cost: number, updatedById?: number) {
        if (cost < 0) {
          throw new Error('Treatment cost cannot be negative');
        }

        return this.update({
          where: { id: treatmentId },
          data: {
            cost,
            updatedById,
            updatedAt: new Date(),
          },
        });
      },
    },
  },
  result: {
    treatment: {
      // Check if treatment is completed
      isCompleted: {
        needs: { status: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.COMPLETED;
        },
      },

      // Check if treatment is pending
      isPending: {
        needs: { status: true },
        compute(treatment) {
          return treatment.status === TreatmentStatus.PENDING;
        },
      },

      // Get formatted cost
      formattedCost: {
        needs: { cost: true },
        compute(treatment) {
          return `$${treatment.cost.toFixed(2)}`;
        },
      },

      // Get status display name
      statusDisplay: {
        needs: { status: true },
        compute(treatment) {
          switch (treatment.status) {
            case TreatmentStatus.PENDING:
              return 'Pending';
            case TreatmentStatus.COMPLETED:
              return 'Completed';
            default:
              return 'Unknown';
          }
        },
      },
    },
  },
});