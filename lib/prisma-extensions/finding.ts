import { Prisma } from "@prisma/client";

export const findingExtension = Prisma.defineExtension({
  name: "findingExtension",
  model: {
    finding: {
      // Get findings for a patient
      async getFindingsForPatient(tenantId: string, patientId: number) {
        return this.findMany({
          where: {
            tenantId,
            tooth: {
              caseSheet: {
                patientId,
              },
            },
          },
          include: {
            tooth: true,
            recordedBy: true,
            treatments: true,
          },
          orderBy: {
            recordedDate: 'desc',
          },
        });
      },

      // Get findings for a specific tooth
      async getFindingsForTooth(tenantId: string, toothId: number) {
        return this.findMany({
          where: {
            tenantId,
            toothId,
          },
          include: {
            recordedBy: true,
            treatments: true,
          },
          orderBy: {
            recordedDate: 'desc',
          },
        });
      },

      // Update finding description
      async updateDescription(findingId: number, description: string, updatedById?: number) {
        return this.update({
          where: { id: findingId },
          data: {
            description,
            updatedById,
          },
        });
      },

      // Create finding with basic validation
      async createFinding(data: {
        tenantId: string;
        toothId: number;
        description: string;
        recordedById?: number;
        createdById?: number;
      }) {
        // Basic validation
        if (!data.description.trim()) {
          throw new Error('Finding description is required');
        }

        if (data.description.length > 1000) {
          throw new Error('Finding description cannot exceed 1000 characters');
        }

        return this.create({
          data: {
            tenantId: data.tenantId,
            toothId: data.toothId,
            description: data.description.trim(),
            recordedById: data.recordedById,
            createdById: data.createdById,
          },
          include: {
            tooth: true,
            recordedBy: true,
            treatments: true,
          },
        });
      },
    },
  },
  result: {
    finding: {
      // Get formatted description with truncation for display
      displayDescription: {
        needs: { description: true },
        compute(finding) {
          if (finding.description.length <= 100) {
            return finding.description;
          }
          return finding.description.substring(0, 97) + '...';
        },
      },

      // Check if finding has treatments
      hasTreatments: {
        needs: { treatments: true },
        compute(finding) {
          return finding.treatments && finding.treatments.length > 0;
        },
      },

      // Get formatted recorded date
      formattedRecordedDate: {
        needs: { recordedDate: true },
        compute(finding) {
          return finding.recordedDate.toLocaleDateString();
        },
      },
    },
  },
});