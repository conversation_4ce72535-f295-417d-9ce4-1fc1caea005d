import { PrismaClient, Prisma } from '@prisma/client';
import { 
  applyAllExtensions, 
  ExtendedPrismaClient, 
  ExtensionConfig, 
  defaultExtensionConfig,
  validateExtensionDependencies 
} from './prisma-extensions';

// Environment-based configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';
const isTest = process.env.NODE_ENV === 'test';

// Database connection configuration
interface DatabaseConfig {
  connectionLimit?: number;
  connectionTimeout?: number;
  poolTimeout?: number;
  logLevel?: Prisma.LogLevel[];
  enableQueryLogging?: boolean;
  enableErrorLogging?: boolean;
  enableInfoLogging?: boolean;
  enableWarnLogging?: boolean;
}

// Default database configuration based on environment
const defaultDatabaseConfig: DatabaseConfig = {
  connectionLimit: isProduction ? 20 : 10,
  connectionTimeout: 20000, // 20 seconds
  poolTimeout: 20000, // 20 seconds
  logLevel: isDevelopment 
    ? ['query', 'info', 'warn', 'error'] 
    : isTest 
      ? ['warn', 'error']
      : ['error'],
  enableQueryLogging: isDevelopment,
  enableErrorLogging: true,
  enableInfoLogging: isDevelopment,
  enableWarnLogging: true,
};

// Global Prisma client type for proper TypeScript support
const globalForPrisma = globalThis as unknown as {
  prisma: ExtendedPrismaClient | undefined;
};

// Client configuration interface
export interface PrismaClientConfig {
  database?: DatabaseConfig;
  extensions?: ExtensionConfig;
  enableMetrics?: boolean;
  enableTracing?: boolean;
}

// Default client configuration
const defaultClientConfig: PrismaClientConfig = {
  database: defaultDatabaseConfig,
  extensions: defaultExtensionConfig,
  enableMetrics: isProduction,
  enableTracing: isDevelopment,
};

/**
 * Creates a new Prisma client with optimized configuration and all extensions applied
 */
function createPrismaClient(config: PrismaClientConfig = defaultClientConfig): ExtendedPrismaClient {
  const dbConfig = { ...defaultDatabaseConfig, ...config.database };
  const extensionConfig = { ...defaultExtensionConfig, ...config.extensions };
  
  // Validate extension dependencies
  const dependencyErrors = validateExtensionDependencies(extensionConfig);
  if (dependencyErrors.length > 0) {
    throw new Error(`Extension dependency validation failed:\n${dependencyErrors.join('\n')}`);
  }
  
  // Build log configuration
  const logConfig: Prisma.LogDefinition[] = [];
  
  if (dbConfig.enableQueryLogging && dbConfig.logLevel?.includes('query')) {
    logConfig.push({
      level: 'query',
      emit: 'event',
    });
  }
  
  if (dbConfig.enableErrorLogging && dbConfig.logLevel?.includes('error')) {
    logConfig.push({
      level: 'error',
      emit: 'event',
    });
  }
  
  if (dbConfig.enableInfoLogging && dbConfig.logLevel?.includes('info')) {
    logConfig.push({
      level: 'info',
      emit: 'event',
    });
  }
  
  if (dbConfig.enableWarnLogging && dbConfig.logLevel?.includes('warn')) {
    logConfig.push({
      level: 'warn',
      emit: 'event',
    });
  }
  
  // Create base Prisma client with optimized configuration
  const baseClient = new PrismaClient({
    log: logConfig,
    datasources: {
      db: {
        url: process.env.DATABASE_URL,
      },
    },
    // Connection pool configuration
    __internal: {
      engine: {
        // Connection pool settings
        connectionLimit: dbConfig.connectionLimit,
        poolTimeout: dbConfig.poolTimeout,
        // Performance optimizations
        binaryTargets: ['native'],
      },
    },
  });
  
  // Set up event listeners for logging
  if (dbConfig.enableQueryLogging) {
    baseClient.$on('query', (e) => {
      if (isDevelopment) {
        console.log('Query: ' + e.query);
        console.log('Params: ' + e.params);
        console.log('Duration: ' + e.duration + 'ms');
      }
    });
  }
  
  if (dbConfig.enableErrorLogging) {
    baseClient.$on('error', (e) => {
      console.error('Prisma Error:', e);
    });
  }
  
  if (dbConfig.enableInfoLogging) {
    baseClient.$on('info', (e) => {
      if (isDevelopment) {
        console.info('Prisma Info:', e.message);
      }
    });
  }
  
  if (dbConfig.enableWarnLogging) {
    baseClient.$on('warn', (e) => {
      console.warn('Prisma Warning:', e.message);
    });
  }
  
  // Add performance monitoring middleware if enabled
  if (config.enableMetrics) {
    baseClient.$use(async (params, next) => {
      const start = Date.now();
      const result = await next(params);
      const end = Date.now();
      
      // Log slow queries in production
      if (isProduction && (end - start) > 1000) {
        console.warn(`Slow query detected: ${params.model}.${params.action} took ${end - start}ms`);
      }
      
      return result;
    });
  }
  
  // Add connection health check
  baseClient.$use(async (params, next) => {
    try {
      return await next(params);
    } catch (error) {
      // Log connection errors
      if (error instanceof Error && error.message.includes('connection')) {
        console.error('Database connection error:', error.message);
      }
      throw error;
    }
  });

  // Apply all extensions to the client
  const extendedClient = applyAllExtensions(baseClient, extensionConfig);
  
  return extendedClient;
}

// Create the main client instance
const client = createPrismaClient();

// Export the main Prisma client instance with full type support
export const prisma: ExtendedPrismaClient = globalForPrisma.prisma ?? client;

// Export types for external use
export type { ExtendedPrismaClient, PrismaClientConfig, DatabaseConfig };

// Export extension-related utilities
export { 
  TenantContext, 
  applyAllExtensions,
  defaultExtensionConfig,
  validateExtensionDependencies 
} from './prisma-extensions';

// Export tenant context manager
export { TenantContextManager } from './tenant-context';

// Utility function to create a new client with custom configuration
export function createCustomPrismaClient(config: PrismaClientConfig): ExtendedPrismaClient {
  return createPrismaClient(config);
}

// Utility function to test database connection
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
}

// Utility function to get database metrics
export async function getDatabaseMetrics() {
  try {
    const metrics = await prisma.$metrics.json();
    return metrics;
  } catch (error) {
    console.warn('Failed to get database metrics:', error);
    return null;
  }
}

// Graceful shutdown handler
export async function closeDatabaseConnection(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log('Database connection closed gracefully');
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
}

// Connection pool status
export async function getConnectionPoolStatus() {
  try {
    // This is a placeholder - actual implementation would depend on Prisma version
    // and available metrics
    return {
      activeConnections: 'N/A',
      idleConnections: 'N/A',
      totalConnections: 'N/A',
    };
  } catch (error) {
    console.warn('Failed to get connection pool status:', error);
    return null;
  }
}

// Set up global client for development hot reloading
if (!isProduction) {
  globalForPrisma.prisma = client;
}

// Handle process termination gracefully
if (isProduction) {
  process.on('SIGINT', async () => {
    await closeDatabaseConnection();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    await closeDatabaseConnection();
    process.exit(0);
  });
}

// Default export
export default prisma;