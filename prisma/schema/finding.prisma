model Finding {
  id            Int       @id @default(autoincrement())
  tenantId      String
  tenant        Tenant    @relation(fields: [tenantId], references: [id])
  
  // Essential relationships
  toothId       Int
  tooth         Tooth     @relation(fields: [toothId], references: [id])
  
  // Simple description (free text as per PRD)
  description   String
  
  // Provider and timing
  recordedById  Int?
  recordedBy    User?     @relation("RecordedBy", fields: [recordedById], references: [id])
  recordedDate  DateTime  @default(now())
  
  // Basic audit fields
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  createdById   Int?
  updatedById   Int?
  
  // Direct relationship to treatments (no diagnosis layer)
  treatments    Treatment[]
  
  @@index([tenantId, toothId])
  @@index([tenantId, recordedDate])
}