
enum UserType {
  ADMIN
  DENTIST
  STAFF
  PATIENT
}

model User {
  id                Int       @id @default(autoincrement())
  tenantId          String
  tenant            Tenant    @relation(fields: [tenantId], references: [id])
  
  // Authentication fields
  username          String?   @unique
  phoneNumber       String?   @unique
  email             String?   @unique
  password          String
  
  // User details
  firstName         String?
  lastName          String?
  userType          UserType  @default(PATIENT)
  
  // Basic status
  isActive          Boolean   @default(true)
  
  // Basic audit fields (preserved for compliance)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  createdById       Int?
  updatedById       Int?
  
  // Relations
  patientProfile           Patient?      @relation("PatientUser")
  primaryAppointments      Appointment[] @relation("PrimaryProvider")
  completedTreatments      Treatment[]   @relation("CompletedBy")
  recordedFindings        Finding[]     @relation("RecordedBy")
  
  @@unique([tenantId, username])
  @@unique([tenantId, phoneNumber])
  @@unique([tenantId, email])
  @@index([tenantId, userType])
}

