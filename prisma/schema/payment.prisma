enum PaymentMethod {
  CASH
  CARD
  CHECK
  BANK_TRANSFER
  OTHER
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
}

model Payment {
  id            Int           @id @default(autoincrement())
  tenantId      String
  tenant        Tenant        @relation(fields: [tenantId], references: [id])
  
  // Essential relationships
  patientId     Int
  patient       Patient       @relation(fields: [patientId], references: [id])
  invoiceId     Int?
  invoice       Invoice?      @relation(fields: [invoiceId], references: [id])
  
  // Basic payment data
  amount        Decimal
  paymentDate   DateTime
  paymentMethod PaymentMethod
  status        PaymentStatus @default(COMPLETED)
  
  // Basic notes
  notes         String?
  
  // Basic audit fields
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  createdById   Int?
  updatedById   Int?
  
  @@index([tenantId, patientId])
  @@index([tenantId, invoiceId])
  @@index([tenantId, paymentDate])
}

