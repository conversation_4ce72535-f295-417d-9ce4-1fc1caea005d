enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
}

model Invoice {
  id                  Int                   @id @default(autoincrement())
  tenantId            String
  tenant              Tenant                @relation(fields: [tenantId], references: [id])
  
  // Essential relationships
  patientId           Int
  patient             Patient               @relation(fields: [patientId], references: [id])
  
  // Essential invoice fields
  invoiceNumber       String                @unique
  invoiceDate         DateTime
  status              InvoiceStatus         @default(DRAFT)
  
  // Essential financial amounts
  totalAmount         Decimal               @default(0.00)
  amountPaid          Decimal               @default(0.00)
  balanceDue          Decimal               @default(0.00)
  
  // Basic audit fields
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  createdById         Int?
  updatedById         Int?
  
  // Essential relationships
  payments            Payment[]
  
  @@unique([tenantId, invoiceNumber])
  @@index([tenantId, patientId])
  @@index([tenantId, status])
}