enum AppointmentType {
  CONSULTATION
  TREATMENT
  CHECKUP
}

enum AppointmentStatus {
  SCHEDULED
  COMPLETED
  CANCELLED
}

model Appointment {
  id                Int               @id @default(autoincrement())
  tenantId          String
  tenant            Tenant            @relation(fields: [tenantId], references: [id])
  
  // Core scheduling
  appointmentDate   DateTime
  durationMinutes   Int               @default(60)
  appointmentType   AppointmentType   @default(CONSULTATION)
  status            AppointmentStatus @default(SCHEDULED)
  
  // Essential relationships
  patientId         Int
  patient           Patient           @relation(fields: [patientId], references: [id])
  primaryProviderId Int?
  primaryProvider   User?             @relation("PrimaryProvider", fields: [primaryProviderId], references: [id])
  
  // Basic notes
  notes             String?
  
  // Basic audit fields
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  createdById       Int?
  updatedById       Int?
  
  @@index([tenantId, patientId, appointmentDate])
  @@index([tenantId, appointmentDate])
  @@index([tenantId, status])
}