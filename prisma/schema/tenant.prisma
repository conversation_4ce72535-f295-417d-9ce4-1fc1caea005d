model Tenant {
  id        String   @id @default(uuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  users               User[]
  patients            Patient[]
  appointments        Appointment[]
  caseSheets          CaseSheet[]
  teeth               Tooth[]
  findings            Finding[]
  treatments          Treatment[]
  invoices            Invoice[]
  payments            Payment[]
}