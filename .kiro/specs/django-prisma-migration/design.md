# Design Document

## Overview

This design outlines the migration of a Django-based dental clinic management system to Prisma ORM while maintaining multitenancy, preserving all existing functionality, and implementing a structured extension system. The migration involves 10 Django models with complex relationships, custom managers, lifecycle hooks, and business logic.

The system will maintain the existing file structure pattern with individual Prisma schema files per model and corresponding extension files. A global multitenancy extension will automatically handle tenant filtering, with bypass capabilities for administrative operations.

## Architecture

### File Structure
```
prisma/
├── schema/
│   ├── main.prisma (generator and datasource)
│   ├── tenant.prisma
│   ├── user.prisma (existing)
│   ├── patient.prisma
│   ├── appointment.prisma
│   ├── case_sheet.prisma
│   ├── tooth.prisma
│   ├── finding.prisma
│   ├── diagnosis.prisma
│   ├── treatment.prisma
│   ├── invoice.prisma
│   └── payment.prisma

lib/
├── prisma.ts (main client with extensions)
├── prisma-extensions/
│   ├── index.ts (combines all extensions)
│   ├── multitenancy.ts (global tenant filtering)
│   ├── user.ts (existing, to be enhanced)
│   ├── patient.ts
│   ├── appointment.ts
│   ├── case_sheet.ts
│   ├── tooth.ts
│   ├── finding.ts
│   ├── diagnosis.ts
│   ├── treatment.ts
│   ├── invoice.ts
│   └── payment.ts
```

### Multitenancy Strategy

The multitenancy will be implemented through a global Prisma extension that:
1. Automatically injects `tenant_id` filters for all read operations
2. Automatically injects `tenant_id` into create/update operations
3. Provides bypass functionality using a `bypassTenant` flag
4. Maintains session context for current tenant ID

### Extension Architecture

Each model will have a corresponding extension file that implements:
1. Django manager methods as Prisma extension methods
2. Computed properties as extension methods
3. Business logic and validation methods
4. Custom query methods and filters

## Components and Interfaces

### 1. Multitenancy Extension

**Location**: `lib/prisma-extensions/multitenancy.ts`

**Purpose**: Global extension that handles automatic tenant filtering and injection

**Key Features**:
- Automatic tenant_id injection for all operations
- Bypass mechanism for administrative queries
- Session context integration
- Support for all CRUD operations

**Interface**:
```typescript
interface MultitenancyExtension {
  getCurrentTenantId(): string;
  bypassTenant<T>(operation: () => Promise<T>): Promise<T>;
}
```

### 2. Model Extensions

Each model extension will implement:

**Common Interface Pattern**:
```typescript
interface ModelExtension<T> {
  // Django manager equivalents
  create(data: CreateInput): Promise<T>;
  findByCustomCriteria(criteria: any): Promise<T[]>;
  
  // Computed properties
  getComputedProperty(): any;
  
  // Business logic methods
  performBusinessOperation(): Promise<void>;
  
  // Validation methods
  validate(): Promise<boolean>;
}
```

### 3. User Extension

**Enhanced from existing**: `lib/prisma-extensions/user.ts`

**Django Managers to Implement**:
- `CustomUserManager` methods
- `AdminManager` filtering
- `ReceptionistManager` filtering  
- `PatientUserManager` filtering
- `DentistUserManager` filtering

**Key Methods**:
- `createUser()` - Handle different user types
- `createSuperuser()` - Admin user creation
- `authenticateByPhone()` - Phone number authentication
- `authenticateByUsername()` - Username authentication
- `getFullName()` - Name formatting
- `setStaffStatus()` - Role-based permissions

### 4. Patient Extension

**Location**: `lib/prisma-extensions/patient.ts`

**Key Methods**:
- `generateClinicId()` - Auto-generate unique clinic ID
- `createCaseSheet()` - Auto-create case sheet on patient creation
- `getAge()` - Calculate age from date of birth
- `getFullName()` - Format full name

### 5. Appointment Extension

**Location**: `lib/prisma-extensions/appointment.ts`

**Key Methods**:
- `generateAppointmentNumber()` - Auto-generate appointment numbers
- `calculateEndTime()` - Auto-calculate end time from duration
- `canBeCancelled()` - Business logic for cancellation
- `canBeRescheduled()` - Business logic for rescheduling
- `createNextRecall()` - Generate recall appointments
- `getStatusColor()` - UI helper for status colors
- `isOverdue()` - Check if appointment is overdue
- `getActualDurationMinutes()` - Calculate actual duration
- `getWaitTimeMinutes()` - Calculate patient wait time

### 6. Treatment Extension

**Location**: `lib/prisma-extensions/treatment.ts`

**Key Methods**:
- `markCompleted()` - Mark treatment as completed
- `markCancelled()` - Cancel treatment with reason
- `autoSetPriorityFromFinding()` - Auto-set priority based on finding
- `getProceduresForFindingCategory()` - Get suggested procedures
- `getCommonProcedures()` - Get frequently used procedures
- `isOverdue()` - Check if treatment is overdue
- `daysSincePlanned()` - Calculate days since planning

## Data Models

### Schema Definitions

#### Tenant Model
```prisma
model Tenant {
  id        String   @id @default(uuid())
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relations
  users         User[]
  patients      Patient[]
  appointments  Appointment[]
  caseSheets    CaseSheet[]
  teeth         Tooth[]
  findings      Finding[]
  diagnoses     Diagnosis[]
  treatments    Treatment[]
  invoices      Invoice[]
  payments      Payment[]
  adjustments   Adjustment[]
}
```

#### Enhanced User Model
```prisma
enum UserType {
  ADMIN
  RECEPTIONIST
  PATIENT
  DENTIST
}

model User {
  id                Int       @id @default(autoincrement())
  tenantId          String
  tenant            Tenant    @relation(fields: [tenantId], references: [id])
  
  // Authentication fields
  username          String?   @unique
  phoneNumber       String?   @unique
  email             String?   @unique
  password          String
  
  // User details
  firstName         String?
  lastName          String?
  userType          UserType  @default(PATIENT)
  
  // Django AbstractUser fields
  isStaff           Boolean   @default(false)
  isActive          Boolean   @default(true)
  isSuperuser       Boolean   @default(false)
  lastLogin         DateTime?
  lastLoginIp       String?
  dateJoined        DateTime  @default(now())
  
  // Audit fields
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  createdById       Int?
  updatedById       Int?
  
  // Self-referential relations for audit
  createdBy         User?     @relation("UserCreatedBy", fields: [createdById], references: [id])
  usersCreated      User[]    @relation("UserCreatedBy")
  updatedBy         User?     @relation("UserUpdatedBy", fields: [updatedById], references: [id])
  usersUpdated      User[]    @relation("UserUpdatedBy")
  
  // Relations
  patientProfile           Patient?      @relation("PatientUser")
  primaryAppointments      Appointment[] @relation("PrimaryProvider")
  assistingAppointments    Appointment[] @relation("AssistingProviders")
  createdAppointments      Appointment[] @relation("CreatedBy")
  modifiedAppointments     Appointment[] @relation("ModifiedBy")
  completedTreatments      Treatment[]   @relation("CompletedBy")
  assignedTreatments       Treatment[]   @relation("AssignedTo")
  madeDiagnoses           Diagnosis[]   @relation("DiagnosingProvider")
  recordedFindings        Finding[]     @relation("RecordedBy")
  receivedPayments        Payment[]     @relation("ReceivedBy")
  processedPayments       Payment[]     @relation("ProcessedBy")
  appliedPayments         PaymentApplication[] @relation("AppliedBy")
  approvedAdjustments     Adjustment[]  @relation("ApprovedBy")
  createdAdjustments      Adjustment[]  @relation("CreatedBy")
  
  @@unique([tenantId, username])
  @@unique([tenantId, phoneNumber])
  @@unique([tenantId, email])
  @@index([tenantId, userType])
  @@index([tenantId, isActive])
}
```

#### Patient Model
```prisma
enum PatientStatus {
  ACTIVE
  INACTIVE
}

model Patient {
  id            Int           @id @default(autoincrement())
  tenantId      String
  tenant        Tenant        @relation(fields: [tenantId], references: [id])
  
  // User relationship
  userId        Int?          @unique
  user          User?         @relation("PatientUser", fields: [userId], references: [id])
  
  // Core demographics
  clinicId      String        @unique
  firstName     String
  lastName      String
  dateOfBirth   DateTime?
  
  // Contact information
  phoneNumber   String?
  email         String?
  address       String?
  
  // Status
  status        PatientStatus @default(ACTIVE)
  
  // Audit fields
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  createdById   Int?
  updatedById   Int?
  
  // Relations
  appointments  Appointment[]
  caseSheet     CaseSheet?
  invoices      Invoice[]
  payments      Payment[]
  adjustments   Adjustment[]
  
  @@unique([tenantId, clinicId])
  @@index([tenantId, lastName, firstName])
  @@index([tenantId, phoneNumber])
  @@index([tenantId, email])
  @@index([tenantId, status])
}
```

#### CaseSheet Model
```prisma
enum CaseSheetStatus {
  ACTIVE
  INACTIVE
}

model CaseSheet {
  id              Int             @id @default(autoincrement())
  tenantId        String
  tenant          Tenant          @relation(fields: [tenantId], references: [id])
  
  // One-to-one relationship with Patient
  patientId       Int             @unique
  patient         Patient         @relation(fields: [patientId], references: [id])
  
  // Clinical information
  clinicalNotes   String?
  status          CaseSheetStatus @default(ACTIVE)
  lastVisitDate   DateTime?
  
  // Audit fields
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  createdById     Int?
  updatedById     Int?
  
  // Relations
  teeth           Tooth[]
  
  @@index([tenantId, patientId])
  @@index([tenantId, status])
  @@index([tenantId, lastVisitDate])
}
```

#### Tooth Model
```prisma
enum ToothStatus {
  PRESENT
  MISSING
  EXTRACTED
  CROWN
  FILLING
}

model Tooth {
  id                  Int         @id @default(autoincrement())
  tenantId            String
  tenant              Tenant      @relation(fields: [tenantId], references: [id])
  
  // Relationships
  caseSheetId         Int
  caseSheet           CaseSheet   @relation(fields: [caseSheetId], references: [id])
  
  // FDI notation
  toothNumber         Int         // 11-48 for adult teeth
  quadrant            Int         // Auto-populated from tooth number
  positionInQuadrant  Int         // Auto-populated from tooth number
  toothName           String      // Auto-populated anatomical name
  
  // Status
  status              ToothStatus @default(PRESENT)
  
  // Audit fields
  createdAt           DateTime    @default(now())
  updatedAt           DateTime    @updatedAt
  createdById         Int?
  updatedById         Int?
  
  // Relations
  findings            Finding[]
  
  @@unique([tenantId, caseSheetId, toothNumber])
  @@index([tenantId, caseSheetId, toothNumber])
  @@index([tenantId, toothNumber])
  @@index([tenantId, status])
}
```

#### Finding Model
```prisma
enum FindingCategory {
  CARIES
  PERIODONTAL
  ENDODONTIC
  ORAL_PATHOLOGY
  ORTHODONTIC
  ORAL_SURGERY
  PROSTHODONTIC
  PREVENTIVE
  TMJ
  TRAUMA
  CONGENITAL
  OTHER
}

enum FindingSeverity {
  MILD
  MODERATE
  SEVERE
  EXTENSIVE
}

enum FindingPrognosis {
  GOOD
  FAIR
  POOR
  HOPELESS
}

enum FindingStatus {
  ACTIVE
  RESOLVED
  MONITORING
}

model Finding {
  id            Int               @id @default(autoincrement())
  tenantId      String
  tenant        Tenant            @relation(fields: [tenantId], references: [id])
  
  // Relationships
  toothId       Int
  tooth         Tooth             @relation(fields: [toothId], references: [id])
  
  // Finding details
  category      FindingCategory
  subcategory   String
  severity      FindingSeverity   @default(MILD)
  prognosis     FindingPrognosis  @default(GOOD)
  status        FindingStatus     @default(ACTIVE)
  
  // Provider and timing
  recordedById  Int?
  recordedBy    User?             @relation("RecordedBy", fields: [recordedById], references: [id])
  recordedDate  DateTime          @default(now())
  
  // Notes
  notes         String?           // Max 500 characters
  
  // Audit fields
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
  createdById   Int?
  updatedById   Int?
  
  // Relations
  diagnoses     Diagnosis[]
  treatments    Treatment[]
  
  @@index([tenantId, toothId, recordedDate])
  @@index([tenantId, category])
  @@index([tenantId, severity])
  @@index([tenantId, prognosis])
  @@index([tenantId, status])
  @@index([tenantId, recordedById])
}
```

#### Diagnosis Model
```prisma
enum DiagnosisCategory {
  CARIES
  PERIODONTAL
  ENDODONTIC
  ORAL_PATHOLOGY
  ORTHODONTIC
  ORAL_SURGERY
  PROSTHODONTIC
  PREVENTIVE
  TMJ
  TRAUMA
  CONGENITAL
  OTHER
}

enum DiagnosisSeverity {
  MILD
  MODERATE
  SEVERE
  EXTENSIVE
}

enum DiagnosisPrognosis {
  GOOD
  FAIR
  POOR
  HOPELESS
}

enum DiagnosisStatus {
  ACTIVE
  STABLE
  RESOLVED
  CHRONIC
  RECURRENT
}

model Diagnosis {
  id                  Int                 @id @default(autoincrement())
  tenantId            String
  tenant              Tenant              @relation(fields: [tenantId], references: [id])
  
  // Relationships
  findingId           Int
  finding             Finding             @relation(fields: [findingId], references: [id])
  
  // Diagnosis details
  diagnosisDate       DateTime            @default(now())
  category            DiagnosisCategory
  severity            DiagnosisSeverity
  prognosis           DiagnosisPrognosis
  status              DiagnosisStatus     @default(ACTIVE)
  
  // Provider
  diagnosingProviderId Int?
  diagnosingProvider  User?               @relation("DiagnosingProvider", fields: [diagnosingProviderId], references: [id])
  
  // Notes
  notes               String?             // Max 500 characters
  
  // Audit fields
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt
  createdById         Int?
  updatedById         Int?
  
  @@index([tenantId, findingId, diagnosisDate])
  @@index([tenantId, category])
  @@index([tenantId, severity])
  @@index([tenantId, prognosis])
  @@index([tenantId, status])
  @@index([tenantId, diagnosingProviderId])
}
```

#### Treatment Model
```prisma
enum TreatmentStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  ON_HOLD
}

enum TreatmentPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

model Treatment {
  id              Int               @id @default(autoincrement())
  tenantId        String
  tenant          Tenant            @relation(fields: [tenantId], references: [id])
  
  // Relationships
  findingId       Int
  finding         Finding           @relation(fields: [findingId], references: [id])
  
  // Treatment details
  procedureCode   String
  procedureName   String
  cost            Decimal           @db.Decimal(10, 2)
  status          TreatmentStatus   @default(PLANNED)
  priority        TreatmentPriority @default(MEDIUM)
  
  // Scheduling
  plannedDate     DateTime?
  completedDate   DateTime?
  
  // Provider assignment
  assignedToId    Int?
  assignedTo      User?             @relation("AssignedTo", fields: [assignedToId], references: [id])
  completedById   Int?
  completedBy     User?             @relation("CompletedBy", fields: [completedById], references: [id])
  
  // Notes
  notes           String?           // Max 1000 characters
  
  // Audit fields
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  createdById     Int?
  updatedById     Int?
  
  @@index([tenantId, findingId, createdAt])
  @@index([tenantId, status])
  @@index([tenantId, priority])
  @@index([tenantId, procedureCode])
  @@index([tenantId, completedDate])
  @@index([tenantId, plannedDate])
  @@index([tenantId, assignedToId])
  @@index([tenantId, completedById])
}
```

#### Invoice Model
```prisma
enum InvoiceStatus {
  DRAFT
  PENDING
  SENT
  VIEWED
  PARTIAL_PAYMENT
  PAID
  OVERDUE
  CANCELLED
  REFUNDED
  WRITE_OFF
}

model Invoice {
  id                  Int                   @id @default(autoincrement())
  tenantId            String
  tenant              Tenant                @relation(fields: [tenantId], references: [id])
  
  // Relationships
  patientId           Int
  patient             Patient               @relation(fields: [patientId], references: [id])
  
  // Invoice details
  invoiceNumber       String                @unique
  invoiceDate         DateTime
  dueDate             DateTime
  status              InvoiceStatus         @default(DRAFT)
  
  // Financial amounts
  subtotal            Decimal               @default(0.00) @db.Decimal(12, 2)
  totalAmount         Decimal               @default(0.00) @db.Decimal(12, 2)
  amountPaid          Decimal               @default(0.00) @db.Decimal(12, 2)
  balanceDue          Decimal               @default(0.00) @db.Decimal(12, 2)
  
  // Payment terms
  paymentTermsDays    Int                   @default(30)
  
  // Text-to-pay functionality
  textToPaySent       Boolean               @default(false)
  textToPayLink       String?
  
  // Audit fields
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  createdById         Int?
  updatedById         Int?
  
  // Relations
  payments            Payment[]
  paymentApplications PaymentApplication[]
  adjustments         Adjustment[]
  
  @@unique([tenantId, invoiceNumber])
  @@index([tenantId, patientId, invoiceDate])
  @@index([tenantId, invoiceNumber])
  @@index([tenantId, status])
  @@index([tenantId, dueDate])
}
```

#### Payment Model
```prisma
enum PaymentMethod {
  CASH
  CHECK
  CREDIT_CARD
  DEBIT_CARD
  BANK_TRANSFER
  ACH
  PAYPAL
  VENMO
  APPLE_PAY
  GOOGLE_PAY
  INSURANCE
  CARE_CREDIT
  PAYMENT_PLAN
  GIFT_CARD
  LOYALTY_POINTS
  OTHER
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
  DISPUTED
  CHARGEBACK
}

enum PaymentSource {
  FRONT_DESK
  ONLINE
  PHONE
  MAIL
  MOBILE_APP
  TEXT_TO_PAY
  AUTO_PAY
  KIOSK
}

enum CardType {
  VISA
  MASTERCARD
  AMEX
  DISCOVER
  OTHER
}

enum ReceiptMethod {
  EMAIL
  SMS
  PRINT
  NONE
}

model Payment {
  id                Int                   @id @default(autoincrement())
  tenantId          String
  tenant            Tenant                @relation(fields: [tenantId], references: [id])
  
  // Relationships
  patientId         Int
  patient           Patient               @relation(fields: [patientId], references: [id])
  invoiceId         Int?
  invoice           Invoice?              @relation(fields: [invoiceId], references: [id])
  
  // Payment details
  paymentNumber     String                @unique
  paymentDate       DateTime
  amount            Decimal               @db.Decimal(10, 2)
  appliedAmount     Decimal               @default(0.00) @db.Decimal(10, 2)
  unappliedAmount   Decimal               @default(0.00) @db.Decimal(10, 2)
  
  // Payment method and processing
  paymentMethod     PaymentMethod
  status            PaymentStatus         @default(COMPLETED)
  paymentSource     PaymentSource         @default(FRONT_DESK)
  
  // Processing details
  transactionId     String?
  processorResponse String?
  
  // Check-specific fields
  checkNumber       String?
  bankName          String?
  
  // Card-specific fields
  cardLastFour      String?
  cardType          CardType?
  
  // Processing fees
  processingFee     Decimal               @default(0.00) @db.Decimal(8, 2)
  netAmount         Decimal               @default(0.00) @db.Decimal(10, 2)
  
  // Staff tracking
  receivedById      Int?
  receivedBy        User?                 @relation("ReceivedBy", fields: [receivedById], references: [id])
  processedById     Int?
  processedBy       User?                 @relation("ProcessedBy", fields: [processedById], references: [id])
  
  // Reconciliation
  isDeposited       Boolean               @default(false)
  depositDate       DateTime?
  depositBatch      String?
  
  // Refund handling
  isRefunded        Boolean               @default(false)
  refundDate        DateTime?
  refundAmount      Decimal?              @db.Decimal(10, 2)
  refundReason      String?
  
  // Notes and receipt
  notes             String?
  internalNotes     String?
  receiptSent       Boolean               @default(false)
  receiptEmail      String?
  receiptMethod     ReceiptMethod         @default(EMAIL)
  
  // Audit fields
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt
  createdById       Int?
  updatedById       Int?
  
  // Relations
  applications      PaymentApplication[]
  
  @@unique([tenantId, paymentNumber])
  @@index([tenantId, patientId, paymentDate])
  @@index([tenantId, paymentNumber])
  @@index([tenantId, status])
  @@index([tenantId, paymentMethod])
  @@index([tenantId, invoiceId])
  @@index([tenantId, transactionId])
}
```

#### PaymentApplication Model
```prisma
model PaymentApplication {
  id              Int      @id @default(autoincrement())
  tenantId        String
  
  // Relationships
  paymentId       Int
  payment         Payment  @relation(fields: [paymentId], references: [id])
  invoiceId       Int
  invoice         Invoice  @relation(fields: [invoiceId], references: [id])
  
  // Application details
  amount          Decimal  @db.Decimal(10, 2)
  applicationDate DateTime @default(now())
  
  // Administrative
  appliedById     Int?
  appliedBy       User?    @relation("AppliedBy", fields: [appliedById], references: [id])
  notes           String?
  
  // Audit fields
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  createdById     Int?
  updatedById     Int?
  
  @@index([tenantId, paymentId, applicationDate])
  @@index([tenantId, invoiceId, applicationDate])
}
```

#### Adjustment Model
```prisma
enum AdjustmentType {
  DISCOUNT
  WRITE_OFF
  COURTESY_ADJUSTMENT
  INSURANCE_ADJUSTMENT
  PROMPT_PAY_DISCOUNT
  FAMILY_DISCOUNT
  SENIOR_DISCOUNT
  HARDSHIP_ADJUSTMENT
  BILLING_ERROR
  OTHER
}

enum AdjustmentStatus {
  PENDING
  APPROVED
  DENIED
  APPLIED
}

model Adjustment {
  id                Int              @id @default(autoincrement())
  tenantId          String
  tenant            Tenant           @relation(fields: [tenantId], references: [id])
  
  // Relationships
  patientId         Int
  patient           Patient          @relation(fields: [patientId], references: [id])
  invoiceId         Int?
  invoice           Invoice?         @relation(fields: [invoiceId], references: [id])
  
  // Adjustment details
  adjustmentNumber  String           @unique
  adjustmentDate    DateTime
  adjustmentType    AdjustmentType
  amount            Decimal          @db.Decimal(10, 2)
  status            AdjustmentStatus @default(PENDING)
  
  // Reason and approval
  reason            String
  notes             String?
  
  // Staff tracking
  approvedById      Int?
  approvedBy        User?            @relation("ApprovedBy", fields: [approvedById], references: [id])
  createdById       Int?
  createdBy         User?            @relation("CreatedBy", fields: [createdById], references: [id])
  
  // Audit fields
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
  
  @@unique([tenantId, adjustmentNumber])
  @@index([tenantId, patientId, adjustmentDate])
  @@index([tenantId, adjustmentType])
  @@index([tenantId, status])
  @@index([tenantId, invoiceId])
}
```

#### Appointment Model (Complete)
```prisma
enum AppointmentType {
  CONSULTATION
  ROUTINE_CHECKUP
  CLEANING
  EMERGENCY
  TREATMENT
  FOLLOW_UP
  SURGERY
  ORTHODONTIC
  RECALL
  NEW_PATIENT
  TELEHEALTH
  BLOCK_TIME
}

enum AppointmentStatus {
  SCHEDULED
  CONFIRMED
  CHECKED_IN
  IN_PROGRESS
  COMPLETED
  NO_SHOW
  CANCELLED
  RESCHEDULED
  LATE_CANCELLATION
}

enum AppointmentPriority {
  EMERGENCY
  URGENT
  ROUTINE
  ELECTIVE
}

enum RecallType {
  ROUTINE_CLEANING
  PERIODONTAL_MAINTENANCE
  ORTHODONTIC_ADJUSTMENT
  POST_TREATMENT
  FLUORIDE_TREATMENT
  ORAL_CANCER_SCREENING
  CUSTOM
}

enum ConfirmationMethod {
  EMAIL
  SMS
  PHONE
  WHATSAPP
  PATIENT_PORTAL
}

enum CancelledBy {
  PATIENT
  PROVIDER
  STAFF
  SYSTEM
}

model Appointment {
  id                      Int                 @id @default(autoincrement())
  tenantId                String
  tenant                  Tenant              @relation(fields: [tenantId], references: [id])
  
  // Core appointment data
  appointmentNumber       String              @unique
  patientId               Int
  patient                 Patient             @relation(fields: [patientId], references: [id])
  
  // Schedule details
  appointmentDate         DateTime
  durationMinutes         Int                 @default(60)
  endTime                 DateTime?
  
  // Appointment details
  appointmentType         AppointmentType     @default(CONSULTATION)
  status                  AppointmentStatus   @default(SCHEDULED)
  priority                AppointmentPriority @default(ROUTINE)
  
  // Provider assignment
  primaryProviderId       Int?
  primaryProvider         User?               @relation("PrimaryProvider", fields: [primaryProviderId], references: [id])
  assistingProviders      User[]              @relation("AssistingProviders")
  
  // Clinical information
  chiefComplaint          String?
  procedureCodes          String?
  clinicalNotes           String?
  
  // Recall functionality
  isRecallAppointment     Boolean             @default(false)
  recallType              RecallType?
  recallIntervalMonths    Int?
  
  // Room and resources
  treatmentRoom           String?
  specialEquipmentNeeded  String?
  
  // Patient communication
  confirmationSent        Boolean             @default(false)
  confirmationMethod      ConfirmationMethod?
  confirmationDate        DateTime?
  reminderSent            Boolean             @default(false)
  reminderDate            DateTime?
  
  // Timing and check-in
  checkInTime             DateTime?
  actualStartTime         DateTime?
  actualEndTime           DateTime?
  
  // Financial information
  estimatedFee            Decimal?            @db.Decimal(10, 2)
  insurancePreAuth        String?
  copayCollected          Decimal?            @db.Decimal(10, 2)
  
  // Special requirements
  specialInstructions     String?
  preMedicationRequired   Boolean             @default(false)
  preMedicationNotes      String?
  wheelchairAccessible    Boolean             @default(false)
  interpreterNeeded       Boolean             @default(false)
  interpreterLanguage     String?
  
  // Cancellation and rescheduling
  cancellationReason      String?
  cancelledBy             CancelledBy?
  cancellationDate        DateTime?
  
  // No-show handling
  noShowFee               Decimal?            @db.Decimal(10, 2)
  noShowNotes             String?
  
  // Follow-up
  followUpNeeded          Boolean             @default(false)
  followUpIntervalWeeks   Int?
  
  // Quality and satisfaction
  patientSatisfactionScore Int?               // 1-5 scale
  patientFeedback         String?
  
  // Audit fields
  createdAt               DateTime            @default(now())
  updatedAt               DateTime            @updatedAt
  createdById             Int?
  updatedById             Int?
  createdBy               User?               @relation("CreatedBy", fields: [createdById], references: [id])
  modifiedBy              User?               @relation("ModifiedBy", fields: [updatedById], references: [id])
  
  // Relations
  rescheduledFromId       Int?
  rescheduledFrom         Appointment?        @relation("RescheduledAppointments", fields: [rescheduledFromId], references: [id])
  rescheduledAppointments Appointment[]       @relation("RescheduledAppointments")
  
  @@unique([tenantId, appointmentNumber])
  @@index([tenantId, patientId, appointmentDate])
  @@index([tenantId, appointmentDate])
  @@index([tenantId, primaryProviderId, appointmentDate])
  @@index([tenantId, status])
  @@index([tenantId, appointmentType])
  @@index([tenantId, isRecallAppointment])
  @@index([tenantId, treatmentRoom, appointmentDate])
}
```

### Relationship Mapping

The design preserves all Django relationships:

1. **One-to-One**: Django `OneToOneField` → Prisma `@unique` relation
2. **Foreign Key**: Django `ForeignKey` → Prisma relation with `@relation`
3. **Many-to-Many**: Django `ManyToManyField` → Prisma implicit many-to-many
4. **Self-referential**: Preserved with named relations

### Enum Definitions

All Django choices are converted to Prisma enums for type safety:
- `UserType` for user types
- `AppointmentType`, `AppointmentStatus`, `AppointmentPriority` for appointments
- `PatientStatus` for patient status
- Treatment, diagnosis, and finding enums

## Error Handling

### Validation Strategy

1. **Schema-level validation**: Prisma schema constraints and types
2. **Extension-level validation**: Custom validation methods in extensions
3. **Business logic validation**: Implemented in extension methods
4. **Tenant isolation**: Automatic tenant filtering prevents cross-tenant access

### Error Types

1. **Validation Errors**: Field validation, business rule violations
2. **Tenant Isolation Errors**: Attempts to access cross-tenant data
3. **Relationship Errors**: Invalid foreign key references
4. **Authentication Errors**: Invalid credentials or permissions

### Error Handling Patterns

```typescript
// Extension method error handling
async createUser(data: UserCreateInput) {
  try {
    // Validation
    await this.validateUserData(data);
    
    // Business logic
    const processedData = await this.processUserCreation(data);
    
    // Create with tenant injection
    return await this.create(processedData);
  } catch (error) {
    if (error instanceof ValidationError) {
      throw new UserValidationError(error.message);
    }
    throw error;
  }
}
```

## Testing Strategy

### Unit Testing

1. **Extension Methods**: Test all Django manager methods and computed properties
2. **Multitenancy**: Verify tenant isolation and bypass functionality
3. **Business Logic**: Test all validation and business rules
4. **Relationships**: Verify all relationship operations

### Integration Testing

1. **End-to-End Workflows**: Test complete user journeys
2. **Authentication**: Test all authentication flows
3. **Data Migration**: Verify data integrity during migration
4. **Performance**: Test query performance and optimization

### Test Structure

```typescript
describe('UserExtension', () => {
  describe('createUser', () => {
    it('should create admin user with correct permissions');
    it('should create patient user with phone authentication');
    it('should enforce tenant isolation');
    it('should validate required fields');
  });
  
  describe('authenticateByPhone', () => {
    it('should authenticate patient by phone number');
    it('should reject invalid credentials');
    it('should respect tenant boundaries');
  });
});
```

### Migration Testing

1. **Data Integrity**: Verify all data migrates correctly
2. **Relationship Preservation**: Ensure all relationships are maintained
3. **Performance Comparison**: Compare query performance before/after
4. **Functionality Parity**: Verify all features work identically

## Performance Considerations

### Query Optimization

1. **Efficient Includes**: Use selective includes to avoid N+1 queries
2. **Index Strategy**: Maintain all Django indexes plus tenant-specific indexes
3. **Connection Pooling**: Implement proper connection management
4. **Query Batching**: Batch related operations where possible

### Multitenancy Performance

1. **Index on tenant_id**: All tenant-filtered queries use indexes
2. **Query Plan Optimization**: Ensure tenant filters are applied early
3. **Connection Per Tenant**: Consider tenant-specific connection pools
4. **Caching Strategy**: Implement tenant-aware caching

### Extension Performance

1. **Lazy Loading**: Implement lazy loading for computed properties
2. **Memoization**: Cache expensive calculations
3. **Bulk Operations**: Provide bulk operation methods
4. **Query Optimization**: Use efficient Prisma query patterns

## Security Considerations

### Tenant Isolation

1. **Automatic Filtering**: All queries automatically filtered by tenant
2. **Bypass Controls**: Strict controls on bypass functionality
3. **Session Management**: Secure tenant context management
4. **Audit Logging**: Log all cross-tenant access attempts

### Authentication Security

1. **Password Hashing**: Maintain secure password hashing
2. **Session Security**: Secure session management
3. **Rate Limiting**: Implement authentication rate limiting
4. **Multi-factor Authentication**: Support for MFA where needed

### Data Protection

1. **Field Encryption**: Encrypt sensitive fields
2. **Access Controls**: Role-based access controls
3. **Audit Trails**: Comprehensive audit logging
4. **Data Retention**: Implement data retention policies