# Implementation Plan

- [x] 1. Remove unnecessary models and clean up references

  - Delete diagnosis.prisma, adjustment.prisma schema files completely
  - Remove diagnosis.ts, adjustment.ts extension files from lib/prisma-extensions/
  - Update lib/prisma-extensions/index.ts to remove deleted model extensions
  - Remove all references to Diagnosis and Adjustment models from other schema files
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6_

- [x] 2. Eliminate service layer completely

  - Delete the entire lib/services/ directory and all service files
  - Update all imports throughout the codebase to remove service layer references
  - Verify that all service layer functionality is available through existing extensions
  - Update any remaining code that uses services to use extensions instead
  - _Requirements: 8.1, 8.2, 8.3, 8.4, 8.5, 8.6_

- [x] 3. Simplify User model and extension

  - Update prisma/schema/user.prisma to remove Django-specific fields (isStaff, isSuperuser, lastLogin, lastLoginIp)
  - Remove complex self-referential audit chains while preserving basic audit fields
  - Simplify UserType enum to 4 values (ADMI<PERSON>, DENTIST, STAFF, PATIENT)
  - Update lib/prisma-extensions/user.ts to remove methods dependent on deleted fields
  - Simplify user creation and authentication methods to work with reduced complexity
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 4. Drastically simplify Appointment model and extension

  - Update prisma/schema/appointment.prisma to reduce from 162 lines to ~30 lines
  - Simplify AppointmentType enum to 3 values (CONSULTATION, TREATMENT, CHECKUP)
  - Remove complex features (recall, room tracking, communication, quality tracking)
  - Preserve core scheduling fields and basic audit fields
  - Update lib/prisma-extensions/appointment.ts to remove complex methods and focus on basic scheduling
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5, 3.6_

- [x] 5. Simplify Finding model and extension

  - Update prisma/schema/finding.prisma to remove categorization enums and complex fields
  - Keep essential fields: tooth relationship, description (free text), recorded by/date, audit fields
  - Remove finding categories, subcategories, severity, prognosis enums
  - Update lib/prisma-extensions/finding.ts to remove categorization methods
  - Ensure direct finding-to-treatment relationship works correctly
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5, 4.6_

- [x] 6. Simplify Treatment model and extension

  - Update prisma/schema/treatment.prisma to reduce TreatmentStatus enum to 2 values (PENDING, COMPLETED)
  - Remove complex workflow fields (priority, plannedDate, assignedToId)
  - Keep essential fields: finding relationship, procedure name, cost, completion tracking, audit fields
  - Update lib/prisma-extensions/treatment.ts to focus on basic treatment completion and cost tracking
  - Ensure direct relationship with findings works without diagnosis layer
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5, 5.6_

- [x] 7. Simplify Payment model and extension

  - Update prisma/schema/payment.prisma to reduce PaymentMethod enum to 5 values (CASH, CARD, CHECK, BANK_TRANSFER, OTHER)
  - Remove complex processing fields (transactionId, processorResponse, cardLastFour, processingFee, etc.)
  - Keep essential fields: amount, date, method, patient/invoice relationship, basic status, audit fields
  - Update lib/prisma-extensions/payment.ts to remove complex processing methods
  - Focus on basic payment creation and tracking functionality
  - _Requirements: 6.1, 6.2, 6.3, 6.4, 6.5, 6.6_

- [x] 8. Simplify Invoice model and extension

  - Update prisma/schema/invoice.prisma to reduce InvoiceStatus enum to 4 values (DRAFT, SENT, PAID, OVERDUE)
  - Remove advanced features (textToPaySent, textToPayLink, complex payment terms)
  - Keep essential fields: invoice number, date, amounts, patient relationship, basic status, audit fields
  - Update lib/prisma-extensions/invoice.ts to remove advanced billing methods
  - Focus on basic invoice management and simplified payment tracking
  - _Requirements: 7.1, 7.2, 7.3, 7.4, 7.5, 7.6_

- [x] 9. Update all enum definitions throughout the codebase

  - Update all enum references in schema files to use simplified enum values
  - Update all enum usage in extension files to work with reduced enum sets
  - Verify that all enum-dependent logic still functions correctly
  - Remove any code that depends on deleted enum values
  - Test that enum simplification doesn't break existing functionality
  - _Requirements: 9.1, 9.2, 9.3, 9.4, 9.5, 9.6, 9.7_

- [x] 10. Optimize database indexes and constraints

  - Review and reduce database indexes from 50+ to ~20 essential indexes
  - Keep tenant isolation indexes, primary lookup patterns, and status-based queries
  - Remove indexes related to deleted fields and complex relationships
  - Update unique constraints to remove unnecessary complexity
  - Verify that essential queries still perform well with reduced index set
  - _Requirements: 11.1, 11.2, 11.3, 11.4, 11.5, 11.6_

- [x] 11. Update extension index and verify audit field functionality

  - Update lib/prisma-extensions/index.ts to properly combine all remaining simplified extensions
  - Verify that basic audit fields (createdById, updatedById, createdAt, updatedAt) are populated correctly in all extensions
  - Test that audit trail functionality works correctly after simplification
  - Ensure that all extension methods properly handle audit field updates
  - Verify that tenant filtering continues to work correctly with simplified models
  - _Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 12.1, 12.2, 12.3, 12.4, 12.5, 12.6_

- [x] 12. Generate and test database migration

  - Run Prisma generate to create new client with simplified models
  - Create database migration script to handle schema changes
  - Test migration on development database to ensure no data loss
  - Verify that all simplified models and relationships work correctly
  - Test that core PRD workflows (finding → treatment → invoice → payment) still function
  - _Requirements: 11.1, 11.2, 11.3, 11.4, 11.5, 11.6, 12.1, 12.2, 12.3, 12.4, 12.5, 12.6_

- [x] 13. Create comprehensive tests for simplified functionality

  - Write unit tests for all simplified extension methods
  - Test that multitenancy continues to work correctly with simplified models
  - Verify that all PRD-specified user flows work after simplification
  - Test audit trail functionality to ensure compliance requirements are met
  - Create integration tests for core workflows (patient → case sheet → findings → treatments → invoicing)
  - _Requirements: 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 12.1, 12.2, 12.3, 12.4, 12.5, 12.6_

- [x] 14. Validate performance improvements and PRD alignment
  - Measure schema size reduction and verify 56% reduction target is met
  - Test database query performance with reduced indexes
  - Verify that all PRD requirements are still satisfied after simplification
  - Confirm that core revenue-generating features (clinical workflow → billing) work correctly
  - Document performance improvements and complexity reduction achieved
  - _Requirements: 11.1, 11.2, 11.3, 11.4, 11.5, 11.6, 12.1, 12.2, 12.3, 12.4, 12.5, 12.6_
