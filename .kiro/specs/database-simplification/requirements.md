# Requirements Document

## Introduction

This feature involves simplifying the current over-engineered database schema to align with the MVP requirements outlined in the PRD. The goal is to reduce complexity by ~56% while maintaining all functionality required by the PRD, following the YAGNI (You Aren't Gonna Need It) principle. The system will continue using Prisma extensions as the primary data access pattern while eliminating unnecessary models, fields, and complexity.

## Requirements

### Requirement 1

**User Story:** As a developer, I want to remove unnecessary models that are not required by the PRD, so that I can reduce system complexity and focus on core MVP functionality.

#### Acceptance Criteria

1. WH<PERSON> removing the Diagnosis model THEN the system SHALL eliminate the separate diagnosis workflow and maintain direct finding-to-treatment workflow as specified in the PRD
2. W<PERSON><PERSON> removing the PaymentApplication model THEN the system SHALL simplify payment tracking to basic payment-to-invoice relationships without complex application mapping
3. WHEN removing the Adjustment model THEN the system SHALL eliminate discount/adjustment functionality not mentioned in the PRD
4. WHEN updating relationships THEN the system SHALL remove all references to deleted models from remaining models
5. WHEN updating extensions THEN the system SHALL remove extension files for deleted models from lib/prisma-extensions/
6. WHEN updating the extension index THEN the system SHALL remove deleted models from lib/prisma-extensions/index.ts

### Requirement 2

**User Story:** As a developer, I want to simplify the User model by removing Django-specific fields and complex audit chains, so that I can maintain essential functionality while reducing complexity.

#### Acceptance Criteria

1. WHEN simplifying the User model THEN the system SHALL remove Django AbstractUser fields (isStaff, isSuperuser, lastLogin, etc.) that are not needed for MVP
2. WHEN handling audit fields THEN the system SHALL preserve basic audit fields (createdById, updatedById, createdAt, updatedAt) for compliance while removing complex self-referential audit chains
3. WHEN simplifying user types THEN the system SHALL reduce UserType enum to 4 values (ADMIN, DENTIST, STAFF, PATIENT) from the complex role system
4. WHEN updating relationships THEN the system SHALL remove complex provider assignment relationships not required by MVP
5. WHEN preserving functionality THEN the system SHALL maintain basic authentication, user type management, and tenant relationships
6. WHEN updating the user extension THEN the system SHALL simplify methods to match the reduced model complexity

### Requirement 3

**User Story:** As a developer, I want to drastically simplify the Appointment model from 162 lines to ~30 lines, so that I can maintain basic scheduling functionality without enterprise-level complexity.

#### Acceptance Criteria

1. WHEN simplifying appointment types THEN the system SHALL reduce from 12 types to 3 types (CONSULTATION, TREATMENT, CHECKUP)
2. WHEN removing complex features THEN the system SHALL eliminate recall functionality, room/equipment tracking, patient communication tracking, financial pre-authorization, and quality tracking
3. WHEN preserving core functionality THEN the system SHALL maintain basic scheduling (date, time, duration), patient relationship, and simple status (SCHEDULED, COMPLETED, CANCELLED)
4. WHEN handling audit fields THEN the system SHALL preserve basic audit fields (createdById, updatedById, createdAt, updatedAt) while removing complex relationship chains
5. WHEN updating the appointment extension THEN the system SHALL remove methods related to deleted functionality and simplify remaining methods
6. WHEN maintaining relationships THEN the system SHALL preserve essential patient and provider relationships while removing complex self-referential rescheduling chains

### Requirement 4

**User Story:** As a developer, I want to simplify the Finding model by removing complex categorization and status tracking, so that I can maintain simple clinical documentation as specified in the PRD.

#### Acceptance Criteria

1. WHEN simplifying findings THEN the system SHALL remove finding categories, subcategories, severity, and prognosis enums that add unnecessary complexity
2. WHEN preserving core functionality THEN the system SHALL maintain tooth relationship, description (free text as per PRD), and direct treatment relationship
3. WHEN handling audit fields THEN the system SHALL preserve basic audit fields (createdById, updatedById, createdAt, updatedAt)
4. WHEN removing complexity THEN the system SHALL eliminate multiple indexes and complex status tracking
5. WHEN updating the finding extension THEN the system SHALL remove categorization methods and simplify to basic finding management
6. WHEN maintaining workflow THEN the system SHALL ensure direct finding-to-treatment workflow continues to work as specified in PRD

### Requirement 5

**User Story:** As a developer, I want to simplify the Treatment model by removing complex workflow and scheduling features, so that I can maintain basic procedure tracking with cost information.

#### Acceptance Criteria

1. WHEN simplifying treatment status THEN the system SHALL reduce status options to 2 values (PENDING, COMPLETED) from the complex workflow system
2. WHEN removing complexity THEN the system SHALL eliminate treatment priority system, complex scheduling (plannedDate), and provider assignment complexity
3. WHEN preserving core functionality THEN the system SHALL maintain finding relationship, procedure name and cost, completion date, and basic status
4. WHEN handling audit fields THEN the system SHALL preserve basic audit fields (createdById, updatedById, createdAt, updatedAt)
5. WHEN updating the treatment extension THEN the system SHALL simplify methods to focus on basic treatment completion and cost tracking
6. WHEN maintaining relationships THEN the system SHALL preserve the direct relationship with findings as specified in the PRD

### Requirement 6

**User Story:** As a developer, I want to simplify the Payment model by removing extensive payment processing complexity, so that I can maintain basic payment tracking functionality.

#### Acceptance Criteria

1. WHEN simplifying payment methods THEN the system SHALL reduce from 17 types to 5 types (CASH, CARD, CHECK, BANK_TRANSFER, OTHER)
2. WHEN removing complexity THEN the system SHALL eliminate complex processing details, card-specific fields, reconciliation features, and receipt tracking
3. WHEN preserving core functionality THEN the system SHALL maintain basic payment (amount, date, method), patient/invoice relationship, and simple status (PENDING, COMPLETED, FAILED)
4. WHEN handling audit fields THEN the system SHALL preserve basic audit fields (createdById, updatedById, createdAt, updatedAt)
5. WHEN updating the payment extension THEN the system SHALL remove complex processing methods and focus on basic payment creation and tracking
6. WHEN maintaining relationships THEN the system SHALL preserve essential patient and invoice relationships

### Requirement 7

**User Story:** As a developer, I want to simplify the Invoice model by removing complex status tracking and advanced features, so that I can maintain basic billing functionality.

#### Acceptance Criteria

1. WHEN simplifying invoice status THEN the system SHALL reduce from 11 statuses to 4 statuses (DRAFT, SENT, PAID, OVERDUE)
2. WHEN removing complexity THEN the system SHALL eliminate text-to-pay functionality and complex payment terms
3. WHEN preserving core functionality THEN the system SHALL maintain basic invoice data (number, date, amounts), patient relationship, and simple status tracking
4. WHEN handling audit fields THEN the system SHALL preserve basic audit fields (createdById, updatedById, createdAt, updatedAt)
5. WHEN updating the invoice extension THEN the system SHALL remove advanced billing methods and focus on basic invoice management
6. WHEN maintaining relationships THEN the system SHALL preserve essential patient relationship and simplified payment tracking

### Requirement 8

**User Story:** As a developer, I want to eliminate the service layer completely, so that I can use Prisma extensions as the sole data access pattern for better type safety and reduced complexity.

#### Acceptance Criteria

1. WHEN removing the service layer THEN the system SHALL delete the lib/services/ directory entirely
2. WHEN consolidating data access THEN the system SHALL ensure all data operations are handled through Prisma extensions only
3. WHEN maintaining functionality THEN the system SHALL verify that all existing service layer functionality is available through extensions
4. WHEN updating imports THEN the system SHALL remove all service layer imports and replace with extension usage
5. WHEN preserving multitenancy THEN the system SHALL ensure built-in multi-tenancy continues to work through extensions
6. WHEN maintaining type safety THEN the system SHALL ensure better TypeScript support through unified extension-based approach

### Requirement 9

**User Story:** As a developer, I want to simplify all enum definitions, so that I can reduce the total enum values from 100+ to ~30 while maintaining essential functionality.

#### Acceptance Criteria

1. WHEN simplifying UserType enum THEN the system SHALL reduce to 4 values (ADMIN, DENTIST, STAFF, PATIENT)
2. WHEN simplifying AppointmentType enum THEN the system SHALL reduce to 3 values (CONSULTATION, TREATMENT, CHECKUP)
3. WHEN simplifying AppointmentStatus enum THEN the system SHALL reduce to 3 values (SCHEDULED, COMPLETED, CANCELLED)
4. WHEN simplifying TreatmentStatus enum THEN the system SHALL reduce to 2 values (PENDING, COMPLETED)
5. WHEN simplifying PaymentMethod enum THEN the system SHALL reduce to 5 values (CASH, CARD, CHECK, BANK_TRANSFER, OTHER)
6. WHEN simplifying InvoiceStatus enum THEN the system SHALL reduce to 4 values (DRAFT, SENT, PAID, OVERDUE)
7. WHEN updating all references THEN the system SHALL ensure all enum usage throughout the codebase is updated to use simplified values

### Requirement 10

**User Story:** As a developer, I want to maintain essential audit fields on all models, so that I can preserve compliance and accountability while removing complex audit relationship chains.

#### Acceptance Criteria

1. WHEN preserving audit fields THEN the system SHALL maintain createdById, updatedById, createdAt, and updatedAt on all models
2. WHEN removing complexity THEN the system SHALL eliminate complex self-referential audit chains (circular createdBy/updatedBy relationships)
3. WHEN ensuring compliance THEN the system SHALL verify that basic audit trail functionality works correctly for tracking who created/modified records
4. WHEN maintaining accountability THEN the system SHALL preserve user activity patterns and change tracking
5. WHEN updating extensions THEN the system SHALL ensure audit field population works correctly in all extension methods
6. WHEN testing audit functionality THEN the system SHALL verify that audit trails are properly maintained after simplification

### Requirement 11

**User Story:** As a developer, I want to reduce database indexes and constraints to essential ones only, so that I can improve performance while maintaining data integrity.

#### Acceptance Criteria

1. WHEN simplifying indexes THEN the system SHALL reduce from 50+ indexes to ~20 essential indexes
2. WHEN preserving data integrity THEN the system SHALL maintain all necessary unique constraints and foreign key relationships
3. WHEN optimizing performance THEN the system SHALL keep indexes that are essential for common query patterns
4. WHEN removing complexity THEN the system SHALL eliminate indexes related to deleted fields and complex relationships
5. WHEN updating schema THEN the system SHALL ensure all remaining indexes are properly defined in Prisma schema files
6. WHEN testing performance THEN the system SHALL verify that essential queries still perform well with reduced index set

### Requirement 12

**User Story:** As a developer, I want to update all existing extensions to work with the simplified models, so that I can maintain functionality while reducing complexity.

#### Acceptance Criteria

1. WHEN updating extensions THEN the system SHALL modify all existing extension files to work with simplified model structures
2. WHEN removing methods THEN the system SHALL eliminate extension methods that depend on deleted fields or models
3. WHEN preserving functionality THEN the system SHALL ensure all essential business logic continues to work through simplified extensions
4. WHEN maintaining patterns THEN the system SHALL follow consistent extension patterns across all simplified models
5. WHEN updating types THEN the system SHALL ensure TypeScript types are correctly updated for all extension methods
6. WHEN testing extensions THEN the system SHALL verify that all extension methods work correctly with simplified models