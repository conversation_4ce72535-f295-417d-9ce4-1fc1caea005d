# Design Document

## Overview

This design outlines the systematic simplification of an over-engineered database schema to align with MVP requirements. The current schema has ~800 lines with 13 models, 20+ enums with 100+ values, and 50+ database indexes. The goal is to reduce this to ~350 lines (56% reduction) with 10 models, ~30 enum values (70% reduction), and ~20 indexes (60% reduction) while maintaining all PRD-specified functionality.

The architecture will continue using Prisma extensions as the sole data access pattern, eliminating the service layer entirely. Basic audit fields will be preserved on all models for compliance while removing complex audit relationship chains.

## Architecture

### Simplification Strategy

The simplification follows a four-phase approach:

1. **Model Elimination**: Remove 3 entire models (Diagnosis, PaymentApplication, Adjustment)
2. **Model Simplification**: Reduce complexity in 7 remaining models by 40-80%
3. **Enum Consolidation**: Reduce enum values from 100+ to ~30
4. **Extension Unification**: Remove service layer, consolidate through extensions

### File Structure Impact

**Before Simplification:**
```
prisma/schema/
├── main.prisma
├── tenant.prisma
├── user.prisma (68 lines)
├── patient.prisma
├── appointment.prisma (162 lines)
├── case_sheet.prisma
├── tooth.prisma
├── finding.prisma (76 lines)
├── diagnosis.prisma (73 lines) ← REMOVE
├── treatment.prisma (59 lines)
├── invoice.prisma (58 lines)
├── payment.prisma (165 lines)
└── adjustment.prisma (58 lines) ← REMOVE

lib/
├── services/ ← REMOVE ENTIRE DIRECTORY
├── prisma-extensions/
│   ├── diagnosis.ts ← REMOVE
│   ├── adjustment.ts ← REMOVE
│   └── [other extensions] ← SIMPLIFY
```

**After Simplification:**
```
prisma/schema/
├── main.prisma
├── tenant.prisma
├── user.prisma (~35 lines)
├── patient.prisma
├── appointment.prisma (~30 lines)
├── case_sheet.prisma
├── tooth.prisma
├── finding.prisma (~20 lines)
├── treatment.prisma (~25 lines)
├── invoice.prisma (~25 lines)
└── payment.prisma (~30 lines)

lib/
├── prisma-extensions/
│   ├── index.ts (updated)
│   ├── multitenancy.ts
│   └── [simplified extensions]
```

### Data Access Architecture

**Current State**: Mixed service layer + extensions
**Target State**: Unified Prisma extensions only

**Benefits of Extension-Only Approach:**
- Seamless Prisma client integration
- Built-in multi-tenancy support
- Rich computed properties for UI
- Better type safety and IntelliSense
- Co-located business logic
- Eliminates service layer boilerplate

## Components and Interfaces

### 1. Model Elimination Strategy

#### 1.1 Diagnosis Model Removal

**Rationale**: PRD specifies "direct finding-to-treatment workflow" with no separate diagnosis step.

**Impact Analysis:**
- **Lines Saved**: 73 lines from schema
- **Extension Removed**: `lib/prisma-extensions/diagnosis.ts`
- **Relationships Affected**: Finding → Treatment (direct relationship)
- **Business Logic**: Eliminate diagnosis categorization, severity tracking

**Migration Strategy:**
```typescript
// Before: Finding → Diagnosis → Treatment
finding.diagnoses[0].treatments

// After: Finding → Treatment (direct)
finding.treatments
```

#### 1.2 PaymentApplication Model Removal

**Rationale**: MVP needs simple payment tracking, not complex payment-to-invoice mapping.

**Impact Analysis:**
- **Lines Saved**: 27 lines from schema
- **Extension Removed**: `lib/prisma-extensions/paymentApplication.ts`
- **Relationships Simplified**: Payment → Invoice (direct relationship)
- **Business Logic**: Eliminate complex payment application logic

**Migration Strategy:**
```typescript
// Before: Complex payment application
payment.applications.forEach(app => app.invoice)

// After: Direct payment-invoice relationship
payment.invoice
```

#### 1.3 Adjustment Model Removal

**Rationale**: Not mentioned in PRD. MVP focuses on basic invoicing.

**Impact Analysis:**
- **Lines Saved**: 58 lines from schema
- **Extension Removed**: `lib/prisma-extensions/adjustment.ts`
- **Features Eliminated**: Discount/adjustment system
- **Business Logic**: Remove advanced billing features

### 2. Model Simplification Strategy

#### 2.1 User Model Simplification

**Current Complexity**: 68 lines with Django-specific fields, complex audit chains
**Target Complexity**: ~35 lines

**Simplification Plan:**

```prisma
// REMOVE: Django AbstractUser fields
isStaff           Boolean   @default(false)  ← REMOVE
isSuperuser       Boolean   @default(false)  ← REMOVE
lastLogin         DateTime?                  ← REMOVE
lastLoginIp       String?                   ← REMOVE

// REMOVE: Complex self-referential audit chains
createdBy         User?     @relation("UserCreatedBy", fields: [createdById], references: [id])  ← REMOVE
usersCreated      User[]    @relation("UserCreatedBy")                                           ← REMOVE

// KEEP: Basic audit fields
createdById       Int?      ← KEEP
updatedById       Int?      ← KEEP
createdAt         DateTime  ← KEEP
updatedAt         DateTime  ← KEEP

// SIMPLIFY: User type enum
enum UserType {
  ADMIN     ← KEEP
  DENTIST   ← KEEP
  STAFF     ← KEEP
  PATIENT   ← KEEP
  // Remove complex role system
}
```

**Extension Impact:**
- Remove Django manager complexity
- Simplify authentication methods
- Focus on basic user creation and type management

#### 2.2 Appointment Model Simplification

**Current Complexity**: 162 lines - massively over-engineered
**Target Complexity**: ~30 lines

**Simplification Plan:**

```prisma
// REMOVE: Complex appointment types (12 → 3)
enum AppointmentType {
  CONSULTATION  ← KEEP
  TREATMENT     ← KEEP
  CHECKUP       ← KEEP
  // Remove: EMERGENCY, SURGERY, ORTHODONTIC, RECALL, etc.
}

// REMOVE: Complex features
recallType              RecallType?           ← REMOVE
specialEquipmentNeeded  String?              ← REMOVE
wheelchairAccessible    Boolean              ← REMOVE
interpreterNeeded       Boolean              ← REMOVE
patientSatisfactionScore Int?                ← REMOVE

// KEEP: Core scheduling
appointmentDate         DateTime             ← KEEP
durationMinutes         Int                  ← KEEP
status                  AppointmentStatus    ← KEEP (simplified)
patientId               Int                  ← KEEP
primaryProviderId       Int?                 ← KEEP
```

**Extension Impact:**
- Remove recall appointment methods
- Remove complex scheduling logic
- Focus on basic appointment CRUD

#### 2.3 Finding Model Simplification

**Current Complexity**: 76 lines with complex categorization
**Target Complexity**: ~20 lines

**Simplification Plan:**

```prisma
// REMOVE: Complex categorization
category      FindingCategory    ← REMOVE
subcategory   String            ← REMOVE
severity      FindingSeverity   ← REMOVE
prognosis     FindingPrognosis  ← REMOVE

// KEEP: Essential fields
toothId       Int               ← KEEP
description   String            ← KEEP (free text as per PRD)
recordedById  Int?              ← KEEP
recordedDate  DateTime          ← KEEP

// KEEP: Basic audit fields
createdById   Int?              ← KEEP
updatedById   Int?              ← KEEP
createdAt     DateTime          ← KEEP
updatedAt     DateTime          ← KEEP
```

**Extension Impact:**
- Remove categorization methods
- Remove severity/prognosis logic
- Focus on simple finding description and treatment linking

#### 2.4 Treatment Model Simplification

**Current Complexity**: 59 lines with complex workflow
**Target Complexity**: ~25 lines

**Simplification Plan:**

```prisma
// REMOVE: Complex workflow
priority        TreatmentPriority  ← REMOVE
plannedDate     DateTime?          ← REMOVE
assignedToId    Int?              ← REMOVE

// SIMPLIFY: Status enum (6 → 2)
enum TreatmentStatus {
  PENDING     ← KEEP
  COMPLETED   ← KEEP
  // Remove: IN_PROGRESS, CANCELLED, ON_HOLD, etc.
}

// KEEP: Essential fields
findingId       Int               ← KEEP
procedureName   String            ← KEEP
cost            Decimal           ← KEEP
completedDate   DateTime?         ← KEEP
completedById   Int?              ← KEEP
```

#### 2.5 Payment Model Simplification

**Current Complexity**: 165 lines - extremely over-engineered
**Target Complexity**: ~30 lines

**Simplification Plan:**

```prisma
// SIMPLIFY: Payment methods (17 → 5)
enum PaymentMethod {
  CASH           ← KEEP
  CARD           ← KEEP (combine CREDIT_CARD, DEBIT_CARD)
  CHECK          ← KEEP
  BANK_TRANSFER  ← KEEP
  OTHER          ← KEEP
  // Remove: PAYPAL, VENMO, APPLE_PAY, etc.
}

// REMOVE: Complex processing details
transactionId     String?    ← REMOVE
processorResponse String?    ← REMOVE
cardLastFour      String?    ← REMOVE
processingFee     Decimal    ← REMOVE
isDeposited       Boolean    ← REMOVE

// KEEP: Essential fields
amount            Decimal    ← KEEP
paymentDate       DateTime   ← KEEP
paymentMethod     PaymentMethod ← KEEP
patientId         Int        ← KEEP
invoiceId         Int?       ← KEEP
```

#### 2.6 Invoice Model Simplification

**Current Complexity**: 58 lines with complex status tracking
**Target Complexity**: ~25 lines

**Simplification Plan:**

```prisma
// SIMPLIFY: Status enum (11 → 4)
enum InvoiceStatus {
  DRAFT     ← KEEP
  SENT      ← KEEP
  PAID      ← KEEP
  OVERDUE   ← KEEP
  // Remove: PENDING, VIEWED, PARTIAL_PAYMENT, etc.
}

// REMOVE: Advanced features
textToPaySent       Boolean   ← REMOVE
textToPayLink       String?   ← REMOVE
paymentTermsDays    Int       ← REMOVE

// KEEP: Essential fields
invoiceNumber       String    ← KEEP
invoiceDate         DateTime  ← KEEP
totalAmount         Decimal   ← KEEP
amountPaid          Decimal   ← KEEP
balanceDue          Decimal   ← KEEP
```

### 3. Extension Simplification Strategy

#### 3.1 Service Layer Elimination

**Current Architecture:**
```typescript
// Service layer approach
class PatientService {
  async createPatient(data: PatientCreateInput) {
    // Business logic
    return this.prisma.patient.create(data);
  }
}

// Usage
const patientService = new PatientService();
const patient = await patientService.createPatient(data);
```

**Target Architecture:**
```typescript
// Extension-only approach
const patient = await prisma.patient.create({
  data: {
    // Extension handles business logic automatically
  }
});
```

**Migration Strategy:**
1. Identify all service layer methods
2. Move business logic to corresponding extensions
3. Update all imports to use extensions
4. Delete `lib/services/` directory

#### 3.2 Extension Method Simplification

**User Extension Simplification:**
```typescript
// Before: Complex Django manager methods
async getAdminUsers() { /* complex filtering */ }
async getReceptionistUsers() { /* complex filtering */ }
async getDentistUsers() { /* complex filtering */ }
async getPatientUsers() { /* complex filtering */ }

// After: Simplified type-based filtering
async getUsersByType(userType: UserType) {
  return this.findMany({
    where: { userType }
  });
}
```

**Appointment Extension Simplification:**
```typescript
// Before: Complex recall system
async createNextRecall() { /* complex recall logic */ }
async getNextRecallDate() { /* complex calculation */ }
async canBeRescheduled() { /* complex business rules */ }

// After: Basic scheduling only
async reschedule(newDate: DateTime) {
  return this.update({
    where: { id: this.id },
    data: { appointmentDate: newDate }
  });
}
```

### 4. Database Schema Optimization

#### 4.1 Index Reduction Strategy

**Current State**: 50+ indexes
**Target State**: ~20 essential indexes

**Index Categories:**

**Keep (Essential for Performance):**
```prisma
// Tenant isolation (critical for multitenancy)
@@index([tenantId])

// Primary lookup patterns
@@index([tenantId, patientId])
@@index([tenantId, appointmentDate])
@@index([tenantId, invoiceNumber])

// Status-based queries
@@index([tenantId, status])
```

**Remove (Unnecessary Complexity):**
```prisma
// Complex categorization indexes
@@index([tenantId, category, subcategory])  ← REMOVE
@@index([tenantId, severity, prognosis])    ← REMOVE

// Rarely used lookup patterns
@@index([tenantId, wheelchairAccessible])   ← REMOVE
@@index([tenantId, interpreterNeeded])      ← REMOVE
```

#### 4.2 Constraint Simplification

**Unique Constraints - Keep Essential:**
```prisma
// Critical business constraints
@@unique([tenantId, clinicId])        ← KEEP
@@unique([tenantId, invoiceNumber])   ← KEEP
@@unique([tenantId, appointmentNumber]) ← KEEP

// Remove complex constraints
@@unique([tenantId, username, email, phoneNumber]) ← SIMPLIFY
```

## Data Models

### Simplified Schema Definitions

#### Simplified User Model
```prisma
enum UserType {
  ADMIN
  DENTIST
  STAFF
  PATIENT
}

model User {
  id              Int       @id @default(autoincrement())
  tenantId        String
  tenant          Tenant    @relation(fields: [tenantId], references: [id])
  
  // Authentication
  username        String?   @unique
  phoneNumber     String?   @unique
  email           String?   @unique
  password        String
  
  // User details
  firstName       String?
  lastName        String?
  userType        UserType  @default(PATIENT)
  isActive        Boolean   @default(true)
  
  // Basic audit fields (preserved for compliance)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  createdById     Int?
  updatedById     Int?
  
  // Essential relationships only
  patientProfile  Patient?  @relation("PatientUser")
  appointments    Appointment[] @relation("PrimaryProvider")
  
  @@unique([tenantId, username])
  @@unique([tenantId, phoneNumber])
  @@index([tenantId, userType])
}
```

#### Simplified Appointment Model
```prisma
enum AppointmentType {
  CONSULTATION
  TREATMENT
  CHECKUP
}

enum AppointmentStatus {
  SCHEDULED
  COMPLETED
  CANCELLED
}

model Appointment {
  id                Int               @id @default(autoincrement())
  tenantId          String
  tenant            Tenant            @relation(fields: [tenantId], references: [id])
  
  // Core scheduling
  appointmentDate   DateTime
  durationMinutes   Int               @default(60)
  appointmentType   AppointmentType   @default(CONSULTATION)
  status            AppointmentStatus @default(SCHEDULED)
  
  // Essential relationships
  patientId         Int
  patient           Patient           @relation(fields: [patientId], references: [id])
  primaryProviderId Int?
  primaryProvider   User?             @relation("PrimaryProvider", fields: [primaryProviderId], references: [id])
  
  // Basic notes
  notes             String?
  
  // Basic audit fields
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  createdById       Int?
  updatedById       Int?
  
  @@index([tenantId, patientId, appointmentDate])
  @@index([tenantId, appointmentDate])
  @@index([tenantId, status])
}
```

#### Simplified Finding Model
```prisma
model Finding {
  id            Int       @id @default(autoincrement())
  tenantId      String
  tenant        Tenant    @relation(fields: [tenantId], references: [id])
  
  // Essential relationships
  toothId       Int
  tooth         Tooth     @relation(fields: [toothId], references: [id])
  
  // Simple description (free text as per PRD)
  description   String
  
  // Provider and timing
  recordedById  Int?
  recordedBy    User?     @relation("RecordedBy", fields: [recordedById], references: [id])
  recordedDate  DateTime  @default(now())
  
  // Basic audit fields
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  createdById   Int?
  updatedById   Int?
  
  // Direct relationship to treatments (no diagnosis layer)
  treatments    Treatment[]
  
  @@index([tenantId, toothId])
  @@index([tenantId, recordedDate])
}
```

#### Simplified Treatment Model
```prisma
enum TreatmentStatus {
  PENDING
  COMPLETED
}

model Treatment {
  id              Int             @id @default(autoincrement())
  tenantId        String
  tenant          Tenant          @relation(fields: [tenantId], references: [id])
  
  // Direct relationship to finding (no diagnosis layer)
  findingId       Int
  finding         Finding         @relation(fields: [findingId], references: [id])
  
  // Essential treatment data
  procedureName   String
  cost            Decimal         @db.Decimal(10, 2)
  status          TreatmentStatus @default(PENDING)
  
  // Completion tracking
  completedDate   DateTime?
  completedById   Int?
  completedBy     User?           @relation("CompletedBy", fields: [completedById], references: [id])
  
  // Basic audit fields
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  createdById     Int?
  updatedById     Int?
  
  @@index([tenantId, findingId])
  @@index([tenantId, status])
}
```

#### Simplified Payment Model
```prisma
enum PaymentMethod {
  CASH
  CARD
  CHECK
  BANK_TRANSFER
  OTHER
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
}

model Payment {
  id            Int           @id @default(autoincrement())
  tenantId      String
  tenant        Tenant        @relation(fields: [tenantId], references: [id])
  
  // Essential relationships
  patientId     Int
  patient       Patient       @relation(fields: [patientId], references: [id])
  invoiceId     Int?
  invoice       Invoice?      @relation(fields: [invoiceId], references: [id])
  
  // Basic payment data
  amount        Decimal       @db.Decimal(10, 2)
  paymentDate   DateTime
  paymentMethod PaymentMethod
  status        PaymentStatus @default(COMPLETED)
  
  // Basic notes
  notes         String?
  
  // Basic audit fields
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  createdById   Int?
  updatedById   Int?
  
  @@index([tenantId, patientId])
  @@index([tenantId, invoiceId])
  @@index([tenantId, paymentDate])
}
```

#### Simplified Invoice Model
```prisma
enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
}

model Invoice {
  id            Int           @id @default(autoincrement())
  tenantId      String
  tenant        Tenant        @relation(fields: [tenantId], references: [id])
  
  // Essential relationships
  patientId     Int
  patient       Patient       @relation(fields: [patientId], references: [id])
  
  // Basic invoice data
  invoiceNumber String        @unique
  invoiceDate   DateTime
  totalAmount   Decimal       @db.Decimal(12, 2)
  amountPaid    Decimal       @default(0.00) @db.Decimal(12, 2)
  balanceDue    Decimal       @default(0.00) @db.Decimal(12, 2)
  status        InvoiceStatus @default(DRAFT)
  
  // Basic audit fields
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  createdById   Int?
  updatedById   Int?
  
  // Simplified relationships
  payments      Payment[]
  
  @@unique([tenantId, invoiceNumber])
  @@index([tenantId, patientId])
  @@index([tenantId, status])
}
```

## Error Handling

### Validation Strategy

**Simplified Validation Approach:**
1. **Schema-level**: Basic Prisma constraints and types
2. **Extension-level**: Essential business rules only
3. **Audit preservation**: Ensure audit fields are populated correctly

**Removed Validation Complexity:**
- Complex categorization validation
- Advanced workflow state validation
- Enterprise-level business rules

### Migration Error Handling

**Data Migration Risks:**
1. **Model Removal**: Ensure no orphaned references
2. **Field Removal**: Verify no critical data loss
3. **Enum Simplification**: Map old values to new simplified values
4. **Relationship Changes**: Update all foreign key references

**Mitigation Strategies:**
1. **Backup Strategy**: Full database backup before migration
2. **Gradual Migration**: Phase-by-phase implementation
3. **Validation Scripts**: Verify data integrity after each phase
4. **Rollback Plan**: Ability to revert changes if issues arise

## Testing Strategy

### Simplified Testing Approach

**Focus Areas:**
1. **Core Functionality**: Verify PRD requirements still work
2. **Audit Preservation**: Ensure audit trails function correctly
3. **Extension Functionality**: Test simplified extension methods
4. **Data Integrity**: Verify relationships work after simplification

**Removed Testing Complexity:**
- Complex workflow testing
- Advanced feature testing
- Enterprise-level integration testing

### Performance Testing

**Key Metrics:**
1. **Query Performance**: Verify essential queries perform well with reduced indexes
2. **Extension Overhead**: Ensure simplified extensions have minimal overhead
3. **Database Size**: Confirm schema size reduction
4. **Memory Usage**: Verify reduced complexity improves memory usage

## Expected Outcomes

### Quantitative Benefits

**Schema Reduction:**
- **Total Lines**: ~800 → ~350 (56% reduction)
- **Model Count**: 13 → 10 (23% reduction)
- **Enum Values**: 100+ → ~30 (70% reduction)
- **Database Indexes**: 50+ → ~20 (60% reduction)

**Code Architecture:**
- **Service Layer**: Eliminated entirely
- **Extension Files**: 3 removed, 7 simplified
- **Import Complexity**: Reduced through unified extension approach

### Qualitative Benefits

**Development Experience:**
- Faster development velocity
- Easier testing and debugging
- Reduced cognitive load
- Simpler API endpoints
- Better type safety through unified extensions

**System Performance:**
- Faster database operations
- Reduced memory usage
- Simpler query patterns
- Improved connection pooling efficiency

**Maintenance Benefits:**
- Easier data migrations
- Simplified backup/restore
- Reduced documentation complexity
- Clearer business logic flow

### PRD Alignment Verification

**Core Requirements Preserved:**
- ✅ "Minimum viable complexity" achieved
- ✅ "Essential clinical data only" maintained
- ✅ "Direct finding-to-treatment workflow" supported
- ✅ "Single case sheet per patient" preserved
- ✅ Core revenue-generating features intact
- ✅ Basic audit trails maintained for compliance

**Eliminated Complexity:**
- ❌ Enterprise-level appointment management
- ❌ Complex payment processing
- ❌ Advanced billing features
- ❌ Separate diagnosis workflow
- ❌ Complex categorization systems

This design ensures the database schema aligns perfectly with the PRD's emphasis on "minimum viable complexity" while maintaining all essential functionality for the dental clinic MVP.